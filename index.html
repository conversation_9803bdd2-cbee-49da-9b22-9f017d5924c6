<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子沙盘展示</title>

    <!-- 性能优化：预加载关键资源 -->
    <link rel="preload" href="node_modules/cesium/Build/Cesium/Cesium.js" as="script">
    <link rel="preload" href="node_modules/cesium/Build/Cesium/Widgets/widgets.css" as="style">
    <link rel="preload" href="src/js/cesium.js" as="script">

    <!-- DNS 预解析外部资源 -->
    <link rel="dns-prefetch" href="//unpkg.com">
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">

    <!-- 安全和性能头部 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="referrer" content="no-referrer-when-downgrade">
    
    <!-- Cesium CDN -->
    <link href="node_modules/cesium/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <link rel="stylesheet" href="src/features/布局/坐标导航/navigation-combined.css">
    <!-- 功能性CSS文件 - 新中文路径 -->
    <link rel="stylesheet" href="src/features/按钮/测量工具/styles/measureTool.css">
    <link rel="stylesheet" href="src/features/按钮/测量工具/styles/measure.css">
    
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="src/css/main.css">
    <link rel="stylesheet" href="src/css/title.css">
    <link rel="stylesheet" href="src/css/buttons.css">
    <link rel="stylesheet" href="src/features/按钮/搜索功能/styles/search.css">
    <link rel="stylesheet" href="src/features/按钮/地形开挖/styles/terrain-dig.css">
    <link rel="stylesheet" href="src/features/按钮/剖面分析/styles/toolbar-buttons.css">
    <link rel="stylesheet" href="src/features/按钮/书签管理/styles/bookmark.css">
    <!-- 添加roamFly样式 -->
    <link rel="stylesheet" href="src/features/按钮/漫游飞行/styles/roamFly.css">
    <!-- 添加打印功能样式 -->
    <link rel="stylesheet" href="src/features/按钮/打印功能/styles/print.css">
    <!-- 添加标记点功能样式 -->
    <link rel="stylesheet" href="src/features/按钮/标记管理/styles/addMarker.css">
    <!-- 添加场景管理功能样式 -->
    <link rel="stylesheet" href="src/features/按钮/场景管理/styles/sceneManager.css">
    <!-- 添加3D建筑功能样式 -->
    <link rel="stylesheet" href="src/features/按钮/三维建筑/styles/building3d.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- 内联关键CSS以减少渲染阻塞 -->
    <style>
        /* 加载指示器样式 */
        .loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            color: white;
            font-family: 'Arial', sans-serif;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 18px;
            margin-bottom: 10px;
        }

        .loading-progress {
            font-size: 14px;
            opacity: 0.8;
        }

        #cesiumContainer {
            width: 100%;
            height: 100vh;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        /* 性能监控面板 */
        .performance-monitor {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            z-index: 9999;
            min-width: 200px;
            display: none;
        }

        .performance-monitor.show {
            display: block;
        }

        .performance-monitor h4 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }

        .performance-monitor .metric {
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
        }

        .performance-monitor .metric .label {
            color: #ccc;
        }

        .performance-monitor .metric .value {
            color: #4CAF50;
            font-weight: bold;
        }

        .performance-toggle {
            position: fixed;
            top: 10px;
            right: 220px;
            background: #4CAF50;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            z-index: 10000;
        }
    </style>

    <!-- JavaScript - 异步加载策略 -->
    <!-- 注意：移除了同步加载的 Cesium.js，改为异步加载 -->

    <!-- 性能监控和异步加载管理器 -->
    <script>
        // 性能监控工具
        class PerformanceMonitor {
            constructor() {
                this.startTime = performance.now();
                this.metrics = {};
                this.setupPerformanceObserver();
                console.log('🚀 性能监控已启动');
            }

            setupPerformanceObserver() {
                if ('PerformanceObserver' in window) {
                    const observer = new PerformanceObserver((list) => {
                        for (const entry of list.getEntries()) {
                            this.metrics[entry.name] = entry.startTime;
                            console.log(`📊 ${entry.name}: ${entry.startTime.toFixed(2)}ms`);
                        }
                    });
                    observer.observe({entryTypes: ['paint', 'largest-contentful-paint']});
                }
            }

            logMetric(name, value) {
                this.metrics[name] = value;
                console.log(`📈 ${name}: ${value.toFixed(2)}ms`);
            }

            showSummary() {
                console.group('🎯 页面加载性能报告');
                Object.entries(this.metrics).forEach(([key, value]) => {
                    console.log(`${key}: ${value.toFixed(2)}ms`);
                });
                console.groupEnd();
            }
        }

        // 异步资源加载器
        class AsyncResourceLoader {
            constructor() {
                this.loadedScripts = new Set();
                this.loadingPromises = new Map();
            }

            async loadScript(src, id = null) {
                if (this.loadedScripts.has(src)) {
                    return Promise.resolve();
                }

                if (this.loadingPromises.has(src)) {
                    return this.loadingPromises.get(src);
                }

                const promise = new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = src;
                    if (id) script.id = id;

                    script.onload = () => {
                        this.loadedScripts.add(src);
                        console.log(`✅ 已加载: ${src}`);
                        resolve();
                    };

                    script.onerror = () => {
                        console.error(`❌ 加载失败: ${src}`);
                        reject(new Error(`Failed to load script: ${src}`));
                    };

                    document.head.appendChild(script);
                });

                this.loadingPromises.set(src, promise);
                return promise;
            }
        }

        // 初始化性能监控和加载器
        const perfMonitor = new PerformanceMonitor();
        const resourceLoader = new AsyncResourceLoader();

        // 更新加载进度
        function updateLoadingProgress(message) {
            const progressElement = document.getElementById('loadingProgress');
            if (progressElement) {
                progressElement.textContent = message;
            }
        }

        // 性能监控面板控制
        function togglePerformanceMonitor() {
            const monitor = document.getElementById('performanceMonitor');
            monitor.classList.toggle('show');
        }

        // 更新性能监控数据
        function updatePerformanceMonitor() {
            // 更新内存使用
            if (performance.memory) {
                const memoryUsed = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                document.getElementById('memoryValue').textContent = `${memoryUsed}MB`;
            }

            // 更新工具数量
            const toolCount = Object.keys(window).filter(key => key.endsWith('UI')).length;
            document.getElementById('toolsValue').textContent = toolCount;

            // 更新 FCP 和 LCP
            if (window.perfMonitor && window.perfMonitor.metrics) {
                const fcp = window.perfMonitor.metrics['first-contentful-paint'];
                const lcp = window.perfMonitor.metrics['largest-contentful-paint'];

                if (fcp) document.getElementById('fcpValue').textContent = `${fcp.toFixed(0)}ms`;
                if (lcp) document.getElementById('lcpValue').textContent = `${lcp.toFixed(0)}ms`;
            }

            // 更新 Cesium 初始化时间
            const cesiumInit = performance.getEntriesByName('cesium-initialization');
            if (cesiumInit.length > 0) {
                document.getElementById('cesiumInitValue').textContent = `${cesiumInit[0].duration.toFixed(0)}ms`;
            }
        }

        // 定期更新性能监控
        setInterval(updatePerformanceMonitor, 2000);
    </script>

    <!-- 确保工具类先加载 -->
    <script src="src/features/工具类/PanelPositioner.js"></script>
    <!-- 高价值优化系统 -->
    <script src="src/features/工具类/EventBus.js"></script>
    <script src="src/features/工具类/ButtonConfig.js"></script>
    <script src="src/features/工具类/PanelManager.js"></script>
    <script src="src/features/工具类/ButtonFactory.js"></script>
    <script src="src/features/工具类/EnhancedToolbarManager.js"></script>
    <!-- 使用示例和演示 -->
    <script src="src/features/工具类/HighValueOptimizationExamples.js"></script>
    <!-- 原有工具栏管理器（备用） -->
    <script src="src/features/工具类/ToolbarManager.js"></script>
    <script src="src/features/工具类/ToolbarManagerExample.js"></script>
    <!-- 注意：移除了 cesium.js 和依赖 Cesium 的脚本，改为异步加载 -->
    <!-- 功能性JS文件 - 新中文路径（不依赖 Cesium 的部分） -->
    <script src="src/features/按钮/搜索功能/core/LocationSearch.js"></script>
    <script src="src/features/布局/坐标导航/CoordinateDisplay.js"></script>
    <script src="src/features/按钮/地形开挖/core/TerrainDigHandler.js"></script>
    <script src="src/features/按钮/剖面分析/core/ProfileAnalysis.js"></script>
    <script src="src/features/按钮/书签管理/core/BookmarkTool.js"></script>
    
    <!-- UI组件JS文件 -->
    <script src="src/features/按钮/搜索功能/ui/SearchUI.js"></script>
    <script src="src/features/按钮/地形开挖/ui/TerrainDigUI.js"></script>
    <script src="src/features/按钮/剖面分析/ui/ProfileAnalysisUI.js"></script>
    <script src="src/features/按钮/测量工具/ui/MeasureUI.js"></script>
    <script src="src/features/按钮/书签管理/ui/BookmarkUI.js"></script>
    <!-- 注意：移除了依赖 Cesium 的模块脚本，改为异步加载 -->
</head>
<body>
    <!-- 加载指示器 -->
    <div id="loadingContainer" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载 3D 数字地球...</div>
        <div id="loadingProgress" class="loading-progress">初始化中...</div>
    </div>

    <!-- 性能监控面板 -->
    <button class="performance-toggle" onclick="togglePerformanceMonitor()">性能监控</button>
    <div id="performanceMonitor" class="performance-monitor">
        <h4>🚀 性能监控</h4>
        <div class="metric">
            <span class="label">FCP:</span>
            <span class="value" id="fcpValue">-</span>
        </div>
        <div class="metric">
            <span class="label">LCP:</span>
            <span class="value" id="lcpValue">-</span>
        </div>
        <div class="metric">
            <span class="label">内存使用:</span>
            <span class="value" id="memoryValue">-</span>
        </div>
        <div class="metric">
            <span class="label">Cesium初始化:</span>
            <span class="value" id="cesiumInitValue">-</span>
        </div>
        <div class="metric">
            <span class="label">工具数量:</span>
            <span class="value" id="toolsValue">-</span>
        </div>
    </div>

    <!-- 标题图片容器 -->
    <div class="title-container">
        <img src="src/images/svg/title.svg" alt="标题">
        <div class="title-text">电子沙盘</div>
    </div>
    
    <div id="cesiumContainer"></div>
    
    <!-- 工具按钮组 - 按钮将由各UI组件动态添加 -->
    <div id="toolButtons"></div>
    
    <!-- 引入SVG图标 -->
    <div id="svg-container"></div>
    
    <script>
        // 异步初始化应用
        async function initializeApp() {
            try {
                console.log('🚀 开始异步加载应用...');
                updateLoadingProgress('正在加载 Cesium 引擎...');

                // 异步加载 Cesium.js
                await resourceLoader.loadScript('node_modules/cesium/Build/Cesium/Cesium.js');
                console.log('✅ Cesium.js 加载完成');

                // 异步加载其他依赖
                updateLoadingProgress('正在加载依赖库...');
                await Promise.all([
                    resourceLoader.loadScript('https://unpkg.com/@turf/turf@6.5.0/turf.min.js'),
                    resourceLoader.loadScript('https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js')
                ]);

                // 加载依赖 Cesium 的脚本
                updateLoadingProgress('正在加载 Cesium 相关模块...');
                await Promise.all([
                    resourceLoader.loadScript('src/features/布局/天空盒/SkyBoxManager.js'),
                    resourceLoader.loadScript('src/features/布局/坐标导航/CesiumNavigation.umd.js'),
                    resourceLoader.loadScript('src/features/按钮/测量工具/core/MeasureTool.js'),
                    resourceLoader.loadScript('src/js/cesium.js')
                ]);

                // 加载工具模块脚本
                updateLoadingProgress('正在加载工具模块...');
                await Promise.all([
                    // 打印功能模块
                    resourceLoader.loadScript('src/features/按钮/打印功能/config.js'),
                    resourceLoader.loadScript('src/features/按钮/打印功能/core/PrintTool.js'),
                    resourceLoader.loadScript('src/features/按钮/打印功能/ui/PrintUI.js'),
                    // 标记管理模块
                    resourceLoader.loadScript('src/features/按钮/标记管理/core/AddMarkerTool.js'),
                    resourceLoader.loadScript('src/features/按钮/标记管理/ui/AddMarkerUI.js'),
                    // 3D建筑模块
                    resourceLoader.loadScript('src/features/按钮/三维建筑/config.js'),
                    resourceLoader.loadScript('src/features/按钮/三维建筑/core/Building3DTool.js'),
                    resourceLoader.loadScript('src/features/按钮/三维建筑/ui/Building3DUI.js')
                ]);

                // 初始化Cesium
                updateLoadingProgress('正在初始化 3D 场景...');
                const viewer = initCesium();
                
                // 分批异步初始化工具以避免阻塞主线程
                await initializeToolsProgressively(viewer);

                // 性能报告
                if (window.perfMonitor) {
                    perfMonitor.showSummary();
                }

                console.log('🎉 应用初始化完成！');

            } catch (error) {
                console.error('❌ 应用初始化失败:', error);

                // 显示错误信息
                const loadingContainer = document.getElementById('loadingContainer');
                if (loadingContainer) {
                    loadingContainer.innerHTML = `
                        <div style="color: #ff6b6b; text-align: center;">
                            <h3>加载失败</h3>
                            <p>应用初始化时发生错误，请刷新页面重试</p>
                            <button onclick="location.reload()" style="
                                padding: 10px 20px;
                                background: #ff6b6b;
                                color: white;
                                border: none;
                                border-radius: 5px;
                                cursor: pointer;
                                margin-top: 10px;
                            ">刷新页面</button>
                        </div>
                    `;
                }
            }
        }

        // 分批异步初始化工具
        async function initializeToolsProgressively(viewer) {
            const tools = [
                {
                    name: '坐标显示',
                    init: () => {
                        if (typeof CoordinateDisplay !== 'undefined') {
                            window.coordinateDisplay = new CoordinateDisplay(viewer);
                        } else {
                            console.warn('⚠️ CoordinateDisplay 未定义，跳过初始化');
                        }
                        return Promise.resolve();
                    }
                },
                {
                    name: '搜索功能',
                    init: () => {
                        if (typeof SearchUI !== 'undefined') {
                            window.searchUI = SearchUI.init(viewer, 'toolButtons');
                        } else {
                            console.warn('⚠️ SearchUI 未定义，跳过初始化');
                        }
                        return Promise.resolve();
                    }
                },
                {
                    name: '地形开挖',
                    init: () => {
                        if (typeof TerrainDigUI !== 'undefined') {
                            window.terrainDigUI = TerrainDigUI.init(viewer, 'toolButtons');
                        } else {
                            console.warn('⚠️ TerrainDigUI 未定义，跳过初始化');
                        }
                        return Promise.resolve();
                    }
                },
                {
                    name: '剖面分析',
                    init: () => {
                        if (typeof ProfileAnalysisUI !== 'undefined') {
                            window.profileAnalysisUI = ProfileAnalysisUI.init(viewer, 'toolButtons');
                        } else {
                            console.warn('⚠️ ProfileAnalysisUI 未定义，跳过初始化');
                        }
                        return Promise.resolve();
                    }
                },
                {
                    name: '测量工具',
                    init: () => {
                        if (typeof MeasureUI !== 'undefined') {
                            return MeasureUI.init(viewer, 'toolButtons').then(measureUI => {
                                window.measureUI = measureUI;
                            }).catch(error => {
                                console.warn('⚠️ 测量工具初始化失败:', error);
                            });
                        } else {
                            console.warn('⚠️ MeasureUI 未定义，跳过初始化');
                            return Promise.resolve();
                        }
                    }
                },
                {
                    name: '书签管理',
                    init: () => {
                        if (typeof BookmarkUI !== 'undefined') {
                            return BookmarkUI.init(viewer, 'toolButtons').then(bookmarkUI => {
                                window.bookmarkUI = bookmarkUI;
                            }).catch(error => {
                                console.warn('⚠️ 书签管理初始化失败:', error);
                            });
                        } else {
                            console.warn('⚠️ BookmarkUI 未定义，跳过初始化');
                            return Promise.resolve();
                        }
                    }
                },
                {
                    name: '打印功能',
                    init: () => {
                        if (typeof PrintUI !== 'undefined') {
                            window.printUI = PrintUI.init(viewer, 'toolButtons');
                        } else {
                            console.warn('⚠️ PrintUI 未定义，跳过初始化');
                        }
                        return Promise.resolve();
                    }
                },
                {
                    name: '标记管理',
                    init: () => {
                        if (typeof AddMarkerUI !== 'undefined') {
                            window.addMarkerUI = AddMarkerUI.init(viewer, 'toolButtons');
                        } else {
                            console.warn('⚠️ AddMarkerUI 未定义，跳过初始化');
                        }
                        return Promise.resolve();
                    }
                },
                {
                    name: '3D建筑',
                    init: () => {
                        if (typeof Building3DUI !== 'undefined') {
                            window.building3DUI = Building3DUI.init(viewer, 'toolButtons');
                        } else {
                            console.warn('⚠️ Building3DUI 未定义，跳过初始化');
                        }
                        return Promise.resolve();
                    }
                }
            ];

            // 分批初始化工具，避免阻塞主线程
            for (const tool of tools) {
                await new Promise(resolve => {
                    requestIdleCallback(async () => {
                        try {
                            updateLoadingProgress(`正在初始化${tool.name}...`);
                            await tool.init();
                            console.log(`✅ ${tool.name}初始化完成`);
                        } catch (error) {
                            console.error(`❌ ${tool.name}初始化失败:`, error);
                        }
                        resolve();
                    });
                });
            }

            // 异步加载场景管理功能和漫游飞行功能
            try {
                updateLoadingProgress('正在加载场景管理功能...');
                const sceneModule = await import('./src/features/按钮/场景管理/index.js');
                window.sceneManagerUI = sceneModule.SceneManagerUI.init(viewer, 'toolButtons');
                console.log('✅ 场景管理初始化完成');
            } catch (error) {
                console.error('❌ 场景管理初始化失败:', error);
            }

            try {
                updateLoadingProgress('正在加载漫游飞行功能...');
                const roamModule = await import('./src/features/按钮/漫游飞行/index.js');
                window.roamFlyUI = roamModule.RoamFlyUI.init(viewer, 'toolButtons');
                console.log('✅ 漫游飞行初始化完成');
            } catch (error) {
                console.error('❌ 漫游飞行初始化失败:', error);
            }

            // 异步加载SVG图标
            try {
                const response = await fetch('src/images/svg/icons.svg');
                const svgContent = await response.text();
                document.getElementById('svg-container').innerHTML = svgContent;
                console.log('✅ SVG图标加载完成');
            } catch (error) {
                console.error('❌ SVG图标加载失败:', error);
            }
        }

        // 等待页面加载完成后开始异步初始化
        window.onload = function() {
            initializeApp();
        };
    </script>
</body>
</html>
