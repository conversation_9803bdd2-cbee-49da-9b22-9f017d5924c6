// 优化的 Cesium 初始化函数
function initCesium() {
    // 性能监控开始
    performance.mark('cesium-init-start');
    console.log('🌍 开始初始化 Cesium...');

    // 更新加载进度
    if (window.updateLoadingProgress) {
        updateLoadingProgress('正在初始化 3D 引擎...');
    }

    // 设置访问令牌
    Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJhM2Y5ZDQxYy0wNWEwLTRkZTAtODI0Mi0wNmQ4MDg1ZTQxZjQiLCJpZCI6MTM4NDM4LCJpYXQiOjE2ODM5NzY5NjV9.nOFHD37Doch8oxs9jkjDJvOe_3yOeypDjxNQpIaZW2U';

    // 初始化 Viewer - 优化配置以提高性能
    const viewer = new Cesium.Viewer('cesiumContainer', {
        // 禁用不必要的UI组件以提高性能
        animation: false,
        baseLayerPicker: false,
        fullscreenButton: false,
        vrButton: false,
        geocoder: false,
        homeButton: false,
        infoBox: false,
        sceneModePicker: false,
        selectionIndicator: false,
        timeline: false,
        navigationHelpButton: false,

        // 延迟加载地形以提高初始化速度
        terrain: undefined,

        // 性能优化设置
        requestRenderMode: true, // 按需渲染
        maximumRenderTimeChange: Infinity,

        // 禁用一些重型功能以加快初始化
        shadows: false,
        terrainShadows: Cesium.ShadowMode.DISABLED
    });
    
    // 将viewer设置为全局变量
    window.viewer = viewer;

    // 更新加载进度
    if (window.updateLoadingProgress) {
        updateLoadingProgress('正在配置地图图层...');
    }

    // 移除默认的影像图层
    viewer.imageryLayers.removeAll();

    // 添加谷歌卫星影像图层
    const googleProvider = new Cesium.UrlTemplateImageryProvider({
        url: 'https://mt{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
        subdomains: ['0', '1', '2', '3'],
        minimumLevel: 1,
        maximumLevel: 22,
        credit: 'Google Earth',
        customTags: {
            s: function() {
                return Math.floor(Math.random() * 4).toString();
            }
        }
    });

    viewer.imageryLayers.addImageryProvider(googleProvider);

    // 延迟加载地形数据以提高初始化速度
    requestIdleCallback(() => {
        if (window.updateLoadingProgress) {
            updateLoadingProgress('正在加载地形数据...');
        }

        viewer.terrainProvider = Cesium.Terrain.fromWorldTerrain({
            requestWaterMask: true,  // 启用水面效果
            requestVertexNormals: true  // 启用地形光照
        });

        console.log('🏔️ 地形数据加载完成');
        viewer.scene.requestRender(); // 请求重新渲染
    });

    // 移除黑色背景
    viewer.scene.globe.baseColor = Cesium.Color.WHITE;
    
    // 基础设置
    viewer.scene.globe.enableLighting = false;  // 启用全球光照
    viewer.scene.skyAtmosphere.show = true;
    viewer.scene.fog.enabled = true;
    viewer.scene.globe.showGroundAtmosphere = true;
    
    // 相机设置
    viewer.scene.screenSpaceCameraController.minimumZoomDistance = 100;
    viewer.scene.screenSpaceCameraController.maximumZoomDistance = 20000000;
    viewer.scene.screenSpaceCameraController.enableTilt = true;  // 启用倾斜，以便更好地观察地形

    // 初始位置
    viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(104.0, 30.0, 17000000),
        orientation: {
            heading: 0.0,
            pitch: -Cesium.Math.PI_OVER_TWO,
            roll: 0.0
        }
    });

    // 限制相机视角范围
    viewer.scene.screenSpaceCameraController.minimumZoomRate = 300;
    viewer.scene.screenSpaceCameraController.maximumZoomRate = 5.0e6;
    viewer.scene.screenSpaceCameraController._maximumMagnitude = 25000000;

    // 添加瓦片加载事件监听
    viewer.scene.globe.tileLoadProgressEvent.addEventListener(function(queuedTileCount) {
        // console.log('正在加载瓦片数量:', queuedTileCount);
    });

    // 开启地形深度测试，提高瓦片显示质量
    viewer.scene.globe.depthTestAgainstTerrain = true;

    // 性能优化设置
    viewer.scene.globe.maximumScreenSpaceError = 2.0; // 降低地形细节以提高性能
    viewer.scene.globe.tileCacheSize = 100; // 减少瓦片缓存以节省内存

    // 启用按需渲染以节省CPU
    viewer.scene.requestRenderMode = true;
    viewer.scene.maximumRenderTimeChange = Infinity;

    // 优化渲染性能
    viewer.scene.logarithmicDepthBuffer = false;
    viewer.scene.fxaa = false; // 禁用抗锯齿以提高性能

    // 移除版权信息
    viewer._cesiumWidget._creditContainer.style.display = "none";

    // 性能监控
    if (window.perfMonitor) {
        perfMonitor.logMetric('cesium-basic-init', performance.now() - performance.getEntriesByName('cesium-init-start')[0].startTime);
    }

    // 初始化导航控件
    const options = {
        enableCompass: true,
        enableZoomControls: true,
        enableDistanceLegend: true,
        enableCompassOuterRing: true
    };
    new CesiumNavigation(viewer, options);

    // 延迟加载世界边界数据以提高初始化速度
    setTimeout(() => {
        if (window.updateLoadingProgress) {
            updateLoadingProgress('正在加载世界地图边界...');
        }

        viewer.dataSources.add(
            Cesium.GeoJsonDataSource.load(
                'https://raw.githubusercontent.com/nvkelso/natural-earth-vector/master/geojson/ne_110m_admin_0_countries.geojson',
                {
                    stroke: Cesium.Color.WHITE,
                    strokeWidth: 2,
                    fill: Cesium.Color.TRANSPARENT,
                    clampToGround: false
                }
            )
        ).then(dataSource => {
        // 获取所有实体
        const entities = dataSource.entities.values;
        // 为每个实体设置高度和标签
        for (let i = 0; i < entities.length; i++) {
            const entity = entities[i];
            if (entity.polygon) {
                // 设置多边形高度
                entity.polygon.height = 10000;
                entity.polygon.extrudedHeight = 0;

                // 获取多边形中心点
                const polyPositions = entity.polygon.hierarchy.getValue().positions;
                const centerCartesian = Cesium.BoundingSphere.fromPoints(polyPositions).center;
                const centerCartographic = Cesium.Cartographic.fromCartesian(centerCartesian);
                const centerLongitude = Cesium.Math.toDegrees(centerCartographic.longitude);
                const centerLatitude = Cesium.Math.toDegrees(centerCartographic.latitude);

                // 获取国家中文名称
                const englishName = entity.properties.NAME;
                // 添加国家名称标签
                viewer.entities.add({
                    position: Cesium.Cartesian3.fromDegrees(
                        centerLongitude,
                        centerLatitude,
                        15000  // 标签高度设置为比边界线再高5000米
                    ),
                    label: {
                        text: englishName,
                        font: '14px Microsoft YaHei',  // 使用微软雅黑字体
                        fillColor: Cesium.Color.WHITE,
                        outlineColor: Cesium.Color.BLACK,
                        outlineWidth: 2,
                        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                        verticalOrigin: Cesium.VerticalOrigin.CENTER,
                        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                        pixelOffset: new Cesium.Cartesian2(0, 0),
                        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 10000000),  // 只在一定距离内显示
                        disableDepthTestDistance: 0,  // 启用深度测试，使标签被地球遮挡
                        scaleByDistance: new Cesium.NearFarScalar(1.0e6, 1.0, 8.0e6, 0.5)  // 距离缩放
                    }
                });
            }
        }
        return dataSource;
    }).catch(error => {
        console.warn('国界线数据加载失败:', error);
    });
    }, 2000); // 延迟2秒加载边界数据

    // 设置场景模式为3D
    viewer.scene.mode = Cesium.SceneMode.SCENE3D;
    
    // 启用批量渲染
    if (Cesium.FeatureDetection.supportsRenderingUint8ArraysToFloatTextures) {
        viewer.scene.context.uniformState.batchTable.featuresLength = 0;
    }
    
    // 配置性能参数
    viewer.scene.logarithmicDepthBuffer = false;
    viewer.scene.requestRenderMode = false;
    viewer.scene.maximumRenderTimeChange = 0.0;
    viewer.scene.debugShowFramesPerSecond = false;

    // 性能监控结束
    performance.mark('cesium-init-end');
    performance.measure('cesium-initialization', 'cesium-init-start', 'cesium-init-end');

    const initTime = performance.getEntriesByName('cesium-initialization')[0].duration;
    console.log(`🎉 Cesium 初始化成功！耗时: ${initTime.toFixed(2)}ms`);

    // 更新加载进度
    if (window.updateLoadingProgress) {
        updateLoadingProgress('初始化完成，正在加载工具...');
    }

    // 初始化天空盒
    try {
        SkyBoxManager.initDefaultSkyBox(viewer);
        console.log('🌌 天空盒初始化成功');
    } catch (error) {
        console.warn('天空盒初始化失败:', error);
    }

    // 内存监控
    if (performance.memory) {
        const memoryInfo = {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
            limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
        };
        console.log('💾 内存使用情况:', memoryInfo);
    }

    let aaa; // 保留原有变量以兼容现有代码

    // 定义函数并添加到window对象使其全局可用
    window.fly = function() {
        viewer.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(98.71707797694049, 27.677299704639537, 50000.0)
        });
    };

    window.clea = function() {
        if (aaa && aaa.clear) {
            aaa.clear();
        }
    };

    // 隐藏加载指示器
    setTimeout(() => {
        const loadingContainer = document.getElementById('loadingContainer');
        if (loadingContainer) {
            loadingContainer.style.opacity = '0';
            setTimeout(() => {
                loadingContainer.style.display = 'none';
                console.log('🎯 加载完成，应用已就绪！');
            }, 500);
        }
    }, 1000);

    return viewer;
}

// 导出初始化函数
window.initCesium = initCesium;
