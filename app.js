require('dotenv').config(); // 加载环境变量
const express = require('express');
const cors = require('cors');
const path = require('path');
const axios = require('axios');
const compression = require('compression');

// 创建Express应用
const app = express();

// 性能监控中间件
app.use((req, res, next) => {
    const start = Date.now();
    res.on('finish', () => {
        const duration = Date.now() - start;
        if (duration > 100) { // 只记录超过100ms的请求
            console.log(`⚠️  慢请求: ${req.method} ${req.url} - ${duration}ms - ${res.statusCode}`);
        }
    });
    next();
});

// 启用Gzip压缩 - 显著减少传输大小
app.use(compression({
    filter: (req, res) => {
        // 不压缩已经压缩的内容或小文件
        if (req.headers['x-no-compression']) {
            return false;
        }
        return compression.filter(req, res);
    },
    level: 6, // 压缩级别 (1-9)，6是性能和压缩率的平衡点
    threshold: 1024, // 只压缩大于1KB的文件
    memLevel: 8 // 内存使用级别
}));

// 启用CORS并配置 - 优化性能
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Cache-Control', 'If-None-Match'],
    credentials: false,
    optionsSuccessStatus: 200 // 支持旧版浏览器
}));

// 启用 JSON 解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 设置静态文件目录
app.use('/static', express.static(path.join(__dirname, 'static'), {
    maxAge: '1y',
    etag: true
}));

// 设置js目录的静态文件服务
app.use('/src/js', express.static(path.join(__dirname, 'src/js'), {
    maxAge: '1y',
    etag: true
}));

// 设置css目录的静态文件服务
app.use('/src/css', express.static(path.join(__dirname, 'src/css'), {
    maxAge: '1y',
    etag: true
}));

// 设置node_modules目录路由，特别是Cesium资源 - 优化缓存策略
app.use('/node_modules/cesium', express.static(path.join(__dirname, 'node_modules/cesium'), {
    maxAge: '1y', // Cesium文件长期缓存
    etag: true,
    immutable: true, // 标记为不可变资源
    setHeaders: (res, filePath) => {
        // 针对不同文件类型设置不同缓存策略
        if (filePath.includes('Cesium.js')) {
            res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
            console.log('📦 Cesium.js 已设置长期缓存');
        } else if (filePath.includes('.css')) {
            res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
        } else if (filePath.includes('.wasm') || filePath.includes('.bin')) {
            res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
        }
    }
}));

// 其他 node_modules 资源
app.use('/node_modules', express.static(path.join(__dirname, 'node_modules'), {
    maxAge: '30d', // 其他依赖30天缓存
    etag: true
}));

// 设置src目录的静态文件服务
app.use('/src', express.static(path.join(__dirname, 'src'), {
    maxAge: '1y',
    etag: true
}));


// 设置src2目录的静态文件服务
app.use('/src2', express.static(path.join(__dirname, 'src2'), {
    maxAge: '1y',
    etag: true
}));

// 主页路由 - 添加性能监控
app.get('/', (req, res) => {
    console.time('页面响应时间');

    // 设置安全头部
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');

    res.sendFile(path.join(__dirname, 'index.html'), (err) => {
        console.timeEnd('页面响应时间');
        if (err) {
            console.error('发送 index.html 失败:', err);
            res.status(500).send('服务器内部错误');
        }
    });
});


// favicon路由
app.get('/favicon.ico', (req, res) => {
    res.status(204).end();
});

// 健康检查路由
app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 设置端口
const PORT = process.env.PORT || 8889;
const HOST = process.env.HOST || '0.0.0.0';

// 启动服务器
app.listen(PORT, HOST, () => {
    console.log(`服务器启动成功！`);
    console.log(`请在浏览器中访问: http://localhost:${PORT}`);
});

// 捕获未处理的异常
process.on('uncaughtException', (err) => {
    console.error('未捕获的异常:', err);
});

// 捕获未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason);
});
