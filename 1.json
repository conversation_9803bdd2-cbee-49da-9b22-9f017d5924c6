{"lighthouseVersion": "12.6.1", "requestedUrl": "http://localhost:8888/", "mainDocumentUrl": "http://localhost:8888/", "finalDisplayedUrl": "http://localhost:8888/", "finalUrl": "http://localhost:8888/", "fetchTime": "2025-08-13T03:21:18.760Z", "gatherMode": "navigation", "runtimeError": {"code": "NO_FCP", "message": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)"}, "runWarnings": ["该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)"], "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "environment": {"hostUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "benchmarkIndex": 1651.5, "credits": {}}, "audits": {"is-on-https": {"id": "is-on-https", "title": "使用 HTTPS", "description": "所有网站都应该通过 HTTPS 来保护，即使网站不处理敏感数据，也应如此。这包括要避免使用[混合内容](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content)（即：通过 HTTPS 实现初始请求，但却通过 HTTP 加载某些资源）。HTTPS 可防止入侵程序篡改或被动地监听您的应用与用户之间的通信，它是 HTTP/2 和许多新的网络平台 API 的先决条件。[详细了解 HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "redirects-http": {"id": "redirects-http", "title": "将 HTTP 流量重定向到 HTTPS", "description": "请务必将所有 HTTP 流量都重定向到 HTTPS，以便为所有用户启用安全的网络功能。[了解详情](https://developer.chrome.com/docs/lighthouse/pwa/redirects-http/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "viewport": {"id": "viewport", "title": "具有包含 `width` 或 `initial-scale` 的 `<meta name=\"viewport\">` 标记", "description": "`<meta name=\"viewport\">` 不仅会针对移动设备屏幕尺寸优化您的应用，还会阻止[系统在响应用户输入前出现 300 毫秒的延迟](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)。[详细了解如何使用视口元标记](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3}, "first-contentful-paint": {"id": "first-contentful-paint", "title": "First Contentful Paint", "description": "First Contentful Paint 标记了绘制出首个文本或首张图片的时间。[详细了解 First Contentful Paint 指标](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "largest-contentful-paint": {"id": "largest-contentful-paint", "title": "Largest Contentful Paint", "description": "Largest Contentful Paint 标记了绘制出最大文本或图片的时间。[详细了解 Largest Contentful Paint 指标](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "first-meaningful-paint": {"id": "first-meaningful-paint", "title": "首次有效绘制时间", "description": "“首次有效绘制时间”测量的是网页主要内容开始对用户显示的时间。[详细了解“首次有效绘制时间”指标](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "speed-index": {"id": "speed-index", "title": "Speed Index", "description": "Speed Index 表明了网页内容的可见填充速度。[详细了解 Speed Index 指标](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "screenshot-thumbnails": {"id": "screenshot-thumbnails", "title": "Screenshot Thumbnails", "description": "This is what the load of your site looked like.", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "final-screenshot": {"id": "final-screenshot", "title": "Final Screenshot", "description": "The last screenshot captured of the pageload.", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "total-blocking-time": {"id": "total-blocking-time", "title": "Total Blocking Time", "description": "当任务用时超过 50 毫秒时计算 FCP 和 Time to Interactive 之间的所有时间段的总和，以毫秒表示。[详细了解 Total Blocking Time 指标](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "max-potential-fid": {"id": "max-potential-fid", "title": "First Input Delay 最长预估值", "description": "您的用户可能会遇到的最长 First Input Delay 是用时最长的任务的耗时。[详细了解 Maximum Potential First Input Delay 指标](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "cumulative-layout-shift": {"id": "cumulative-layout-shift", "title": "Cumulative Layout Shift", "description": "Cumulative Layout Shift 旨在衡量可见元素在视口内的移动情况。[详细了解 Cumulative Layout Shift 指标](https://web.dev/articles/cls)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "errors-in-console": {"id": "errors-in-console", "title": "控制台日志中未记录浏览器错误", "description": "控制台中记录的错误表明有未解决的问题。这些问题的起因可能是网络请求失败和其他浏览器问题。[在控制台诊断审核中详细了解此错误](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "server-response-time": {"id": "server-response-time", "title": "初始服务器响应用时较短", "description": "请确保服务器响应主文档的用时较短，因为这会影响到所有其他请求的响应时长。[详细了解 Time to First Byte 指标](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 1}, "interactive": {"id": "interactive", "title": "Time to Interactive", "description": "“Time to Interactive”是指网页需要多长时间才能提供完整的交互功能。[详细了解“Time to Interactive”指标](https://developer.chrome.com/docs/lighthouse/performance/interactive/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "user-timings": {"id": "user-timings", "title": "User Timing 标记和测量结果", "description": "建议使用 User Timing API 对您的应用进行插桩，从而衡量应用在关键用户体验中的实际性能。[详细了解 User Timing 标记](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 2}, "critical-request-chains": {"id": "critical-request-chains", "title": "避免链接关键请求", "description": "下面的关键请求链显示了以高优先级加载的资源。建议缩短链长、缩减资源的下载文件大小，或者推迟下载不必要的资源，从而提高网页加载速度。[了解如何避免链接关键请求](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 1}, "redirects": {"id": "redirects", "title": "避免多次网页重定向", "description": "重定向会在网页可加载前引入更多延迟。[了解如何避免网页重定向](https://developer.chrome.com/docs/lighthouse/performance/redirects/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 2}, "image-aspect-ratio": {"id": "image-aspect-ratio", "title": "显示的图像宽高比正确", "description": "图像显示尺寸应与自然宽高比相匹配。[详细了解图片宽高比](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "image-size-responsive": {"id": "image-size-responsive", "title": "所提供的图片都采用了合适的分辨率", "description": "图片的自然尺寸应与显示屏大小及像素比成正比，这样才能令图片达到最清晰的显示效果。[了解如何提供自适应图片](https://web.dev/articles/serve-responsive-images)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "deprecations": {"id": "deprecations", "title": "请勿使用已弃用的 API", "description": "已弃用的 API 最终将从浏览器中移除。[详细了解已弃用的 API](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "third-party-cookies": {"id": "third-party-cookies", "title": "避免使用第三方 Cookie", "description": "Chrome 将推出新的用户体验，使用户可以选择不使用第三方 Cookie 来浏览网页。[详细了解第三方 Cookie](https://developers.google.com/privacy-sandbox/cookies)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "mainthread-work-breakdown": {"id": "mainthread-work-breakdown", "title": "最大限度地减少主线程工作", "description": "建议您减少为解析、编译和执行 JS 而花费的时间。您可能会发现，提供较小的 JS 载荷有助于实现此目标。[了解如何尽可能减少主线程工作](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 1}, "bootup-time": {"id": "bootup-time", "title": "JavaScript 执行用时", "description": "建议您减少为解析、编译和执行 JS 而花费的时间。您可能会发现，提供较小的 JS 载荷有助于实现此目标。[了解如何缩短 JavaScript 执行时间](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 1}, "uses-rel-preconnect": {"id": "uses-rel-preconnect", "title": "预先连接到必要的来源", "description": "建议添加 `preconnect` 或 `dns-prefetch` 资源提示，以尽早连接到重要的第三方源。[了解如何预先连接到必需的源](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3}, "font-display": {"id": "font-display", "title": "在网页字体加载期间，所有文本都保持可见状态", "description": "利用 `font-display` CSS 功能来确保用户在网页字体加载期间能看到文字。[详细了解 `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3}, "diagnostics": {"id": "diagnostics", "title": "Diagnostics", "description": "Collection of useful page vitals.", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "network-requests": {"id": "network-requests", "title": "Network Requests", "description": "Lists the network requests that were made during page load.", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "network-rtt": {"id": "network-rtt", "title": "网络往返时间", "description": "网络往返时间 (RTT) 对性能有很大的影响。如果与来源之间的 RTT 较长，则表明缩短服务器与用户之间的距离可能会提高性能。[详细了解往返时间](https://hpbn.co/primer-on-latency-and-bandwidth/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "network-server-latency": {"id": "network-server-latency", "title": "服务器后端延迟", "description": "服务器延迟时间可能会对网站性能产生不良影响。如果源服务器的延迟时间较长，则表明服务器过载或后端性能较差。[详细了解服务器响应时间](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "main-thread-tasks": {"id": "main-thread-tasks", "title": "Tasks", "description": "Lists the toplevel main thread tasks that executed during page load.", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "metrics": {"id": "metrics", "title": "Metrics", "description": "Collects all available metrics.", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "resource-summary": {"id": "resource-summary", "title": "Resources Summary", "description": "Aggregates all network requests and groups them by type", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "third-party-summary": {"id": "third-party-summary", "title": "尽量减少第三方使用", "description": "第三方代码可能会显著影响加载性能。请限制冗余第三方提供商的数量，并尝试在页面完成主要加载后再加载第三方代码。[了解如何将第三方的影响降至最低](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 1}, "third-party-facades": {"id": "third-party-facades", "title": "使用 Facade 延迟加载第三方资源", "description": "您可以延迟加载某些第三方嵌入代码。建议使用 Facade 替换这些代码，直到您需要使用它们为止。[了解如何使用 Facade 推迟加载第三方代码](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3}, "largest-contentful-paint-element": {"id": "largest-contentful-paint-element", "title": "最大内容渲染时间元素", "description": "这是此视口内绘制的最大内容元素。[详细了解 Largest Contentful Paint 元素](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 1}, "lcp-lazy-loaded": {"id": "lcp-lazy-loaded", "title": "Largest Contentful Paint 所对应的图片未被延迟加载", "description": "被延迟加载的首屏图片会在页面生命周期内的较晚时间渲染，这可能会致使系统延迟 Largest Contentful Paint。[详细了解最佳延迟加载](https://web.dev/articles/lcp-lazy-loading)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3}, "layout-shifts": {"id": "layout-shifts", "title": "请避免出现大幅度的布局偏移", "description": "这些是在网页上观察到的最大幅的布局偏移。表格中的每个项均表示一次布局偏移，并显示了发生最大幅偏移的元素。每个项的下方列出了哪些可能的根本原因导致了相应的布局偏移。由于[窗口化](https://web.dev/articles/cls#what_is_cls)的原因，所显示的某些布局偏移可能未包含在 CLS 指标值中。[了解如何改善 CLS](https://web.dev/articles/optimize-cls)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 2}, "long-tasks": {"id": "long-tasks", "title": "应避免出现长时间运行的主线程任务", "description": "列出了主线程中运行时间最长的任务，有助于识别出导致输入延迟的最主要原因。[了解如何避免出现长时间运行的主线程任务](https://web.dev/articles/optimize-long-tasks)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 1}, "non-composited-animations": {"id": "non-composited-animations", "title": "避免使用未合成的动画", "description": "未合成的动画可能会卡顿并增加 CLS。[了解如何避免使用未合成的动画](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 2}, "unsized-images": {"id": "unsized-images", "title": "图片元素都有明确的`width`和`height`", "description": "请为图片元素设置明确的宽度值和高度值，以减少布局偏移并改善 CLS。[了解如何设置图片尺寸](https://web.dev/articles/optimize-cls#images_without_dimensions)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 4}, "valid-source-maps": {"id": "valid-source-maps", "title": "页面包含有效的源代码映射", "description": "源代码映射会将缩减了大小的代码转换成原始源代码。这有助于开发者在正式版中调试。此外，Lighthouse 还能提供进一步的数据分析。建议部署源代码映射以充分利用这些好处。[详细了解源代码映射](https://developer.chrome.com/docs/devtools/javascript/source-maps/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "prioritize-lcp-image": {"id": "prioritize-lcp-image", "title": "预加载 LCP 元素所用图片", "description": "如果向网页中动态添加 LCP 元素，您应预加载图片以缩短 LCP 用时。[详细了解如何预加载 LCP 元素](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 4}, "csp-xss": {"id": "csp-xss", "title": "请确保 CSP 能够有效地抵御 XSS 攻击", "description": "强有力的内容安全政策 (CSP) 可显著降低遭遇跨站脚本攻击 (XSS) 的风险。[了解如何使用 CSP 来防止 XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "has-hsts": {"id": "has-hsts", "title": "使用严格 HSTS 政策", "description": "部署 HSTS 标头可显著降低 HTTP 连接降级和窃听攻击的风险。建议分阶段部署，从较低的 max-age 开始。[详细了解如何使用严格 HSTS 政策。](https://developer.chrome.com/docs/lighthouse/best-practices/has-hsts)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "origin-isolation": {"id": "origin-isolation", "title": "确保通过 COOP 实现适当的源隔离", "description": "Cross-Origin-Opener-Policy (COOP) 可用于将顶级窗口与其他文档（例如弹出式窗口）隔离。[详细了解如何部署 COOP 标头。](https://web.dev/articles/why-coop-coep#coop)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "clickjacking-mitigation": {"id": "clickjacking-mitigation", "title": "使用 XFO 或 CSP 缓解点击劫持问题", "description": "`X-Frame-Options` (XFO) 标头或 `Content-Security-Policy` (CSP) 标头中的 `frame-ancestors` 指令控制网页可嵌入的位置。这些设置可以阻止部分或全部网站嵌入网页，从而缓解点击劫持攻击。[详细了解如何缓解点击劫持](https://developer.chrome.com/docs/lighthouse/best-practices/clickjacking-mitigation)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "script-treemap-data": {"id": "script-treemap-data", "title": "Script Treemap Data", "description": "Used for treemap app", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "accesskeys": {"id": "accesskeys", "title": "“`[accesskey]`”值是独一无二的", "description": "快捷键可让用户快速转到页面的某个部分。为确保正确进行导航，每个快捷键都必须是独一无二的。[详细了解快捷键](https://dequeuniversity.com/rules/axe/4.10/accesskeys)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-allowed-attr": {"id": "aria-allowed-attr", "title": "`[aria-*]` 属性与其角色匹配", "description": "每个 ARIA“`role`”都支持一部分特定的“`aria-*`”属性。如果这些项不匹配，会导致“`aria-*`”属性无效。[了解如何将 ARIA 属性与其角色进行匹配](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-attr)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-allowed-role": {"id": "aria-allowed-role", "title": "使用 ARIA 角色的元素均为兼容元素", "description": "许多 HTML 元素只能设置特定的 ARIA 角色。在禁止使用 ARIA 角色的位置使用 ARIA 角色可能会干扰网页的无障碍功能。[详细了解 ARIA 角色](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-role)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-command-name": {"id": "aria-command-name", "title": "`button`、`link` 和 `menuitem` 元素都有可供访问的名称", "description": "如果某个元素没有无障碍名称，屏幕阅读器会将它读为通用名称，这会导致依赖屏幕阅读器的用户无法使用它。[了解如何让命令元素更便于使用](https://dequeuniversity.com/rules/axe/4.10/aria-command-name)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-conditional-attr": {"id": "aria-conditional-attr", "title": "按照指定方式在元素角色中使用了 ARIA 属性", "description": "只能在特定条件下使用元素的某些 ARIA 属性。[详细了解条件性 ARIA 属性](https://dequeuniversity.com/rules/axe/4.10/aria-conditional-attr)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-deprecated-role": {"id": "aria-deprecated-role", "title": "未使用已弃用的 ARIA 角色", "description": "辅助技术可能无法正确处理已弃用的 ARIA 角色。[详细了解已弃用的 ARIA 角色](https://dequeuniversity.com/rules/axe/4.10/aria-deprecated-role)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-dialog-name": {"id": "aria-dialog-name", "title": "具有 `role=\"dialog\"` 或 `role=\"alertdialog\"` 的元素均有无障碍名称。", "description": "如果 ARIA 对话框元素缺少无障碍名称，可能会妨碍屏幕阅读器用户理解这些元素的用途。[了解如何让 ARIA 对话框元素更符合无障碍需求](https://dequeuniversity.com/rules/axe/4.10/aria-dialog-name)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-hidden-body": {"id": "aria-hidden-body", "title": "文档 `<body>` 中没有 `[aria-hidden=\"true\"]`", "description": "在文档 `<body>` 上设置 `aria-hidden=\"true\"` 后，辅助技术（如屏幕阅读器）的工作方式不一致。[了解 `aria-hidden` 对文档正文的影响](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-body)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-hidden-focus": {"id": "aria-hidden-focus", "title": "`[aria-hidden=\"true\"]` 元素都不含可聚焦的下级元素", "description": "有一个 `[aria-hidden=\"true\"]` 元素包含可聚焦的下级元素，导致这些交互式元素都无法被辅助技术（如屏幕阅读器）用户使用。[了解 `aria-hidden` 如何影响可聚焦的元素](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-focus)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-input-field-name": {"id": "aria-input-field-name", "title": "ARIA 输入字段都有可供访问的名称", "description": "如果某个输入字段没有无障碍名称，屏幕阅读器会将它读为通用名称，这会导致依赖屏幕阅读器的用户无法使用它。[详细了解输入字段标签](https://dequeuniversity.com/rules/axe/4.10/aria-input-field-name)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-meter-name": {"id": "aria-meter-name", "title": "ARIA `meter` 元素都有可供访问的名称", "description": "如果某个 meter 元素没有无障碍名称，屏幕阅读器会将它读为通用名称，这会导致依赖屏幕阅读器的用户无法使用它。[了解如何为 `meter` 元素命名](https://dequeuniversity.com/rules/axe/4.10/aria-meter-name)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-progressbar-name": {"id": "aria-progressbar-name", "title": "ARIA `progressbar` 元素都有可供访问的名称", "description": "如果某个 `progressbar` 元素没有无障碍名称，屏幕阅读器会将它读为通用名称，这会导致依赖屏幕阅读器的用户无法使用它。[了解如何为 `progressbar` 元素添加标签](https://dequeuniversity.com/rules/axe/4.10/aria-progressbar-name)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-prohibited-attr": {"id": "aria-prohibited-attr", "title": "元素仅使用允许的 ARIA 属性", "description": "在禁止使用 ARIA 属性的角色中使用 ARIA 属性可能会导致重要信息无法传达给使用辅助技术的用户。[详细了解被禁止的 ARIA 角色](https://dequeuniversity.com/rules/axe/4.10/aria-prohibited-attr)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-required-attr": {"id": "aria-required-attr", "title": "`[role]` 具备所有必需的 `[aria-*]` 属性", "description": "一些 ARIA 角色有必需属性，这些属性向屏幕阅读器描述了相应元素的状态。[详细了解角色和必需属性](https://dequeuniversity.com/rules/axe/4.10/aria-required-attr)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-required-children": {"id": "aria-required-children", "title": "具有 ARIA `[role]`且要求子元素必须包含特定`[role]`的元素包含全部必需子元素。", "description": "一些 ARIA 父角色必须包含特定子角色，才能执行它们的预期无障碍功能。[详细了解角色和必需的子元素](https://dequeuniversity.com/rules/axe/4.10/aria-required-children)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-required-parent": {"id": "aria-required-parent", "title": "`[role]` 包含在其必需的父元素中", "description": "一些 ARIA 子角色必须包含在特定的父角色中，才能正确执行它们的预期无障碍功能。[详细了解 ARIA 角色和必需的父元素](https://dequeuniversity.com/rules/axe/4.10/aria-required-parent)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-roles": {"id": "aria-roles", "title": "`[role]` 值有效", "description": "ARIA 角色必须具备有效值，才能执行它们的预期无障碍功能。[详细了解有效的 ARIA 角色](https://dequeuniversity.com/rules/axe/4.10/aria-roles)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-text": {"id": "aria-text", "title": "具有 `role=text` 属性的元素缺少可聚焦后代元素。", "description": "在一个被标记分隔的文本节点周围添加 `role=text` 后，VoiceOver 会将该文本元素视为一个短语，但不会读出它的可聚焦后代元素。[详细了解 `role=text` 属性](https://dequeuniversity.com/rules/axe/4.10/aria-text)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-toggle-field-name": {"id": "aria-toggle-field-name", "title": "ARIA 切换字段都有可供访问的名称", "description": "如果某个切换字段没有无障碍名称，屏幕阅读器会将它读为通用名称，这会导致依赖屏幕阅读器的用户无法使用它。[详细了解切换字段](https://dequeuniversity.com/rules/axe/4.10/aria-toggle-field-name)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-tooltip-name": {"id": "aria-tooltip-name", "title": "ARIA `tooltip` 元素都有可供访问的名称", "description": "如果某个 tooltip 元素没有无障碍名称，屏幕阅读器会将它读为通用名称，这会导致依赖屏幕阅读器的用户无法使用它。[了解如何为 `tooltip` 元素命名](https://dequeuniversity.com/rules/axe/4.10/aria-tooltip-name)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-treeitem-name": {"id": "aria-treeitem-name", "title": "ARIA `treeitem` 元素都有可供访问的名称", "description": "如果某个 `treeitem` 元素没有无障碍名称，屏幕阅读器会将它读为通用名称，这会导致依赖屏幕阅读器的用户无法使用它。[详细了解如何为 `treeitem` 元素添加标签](https://dequeuniversity.com/rules/axe/4.10/aria-treeitem-name)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-valid-attr-value": {"id": "aria-valid-attr-value", "title": "`[aria-*]` 属性具备有效值", "description": "辅助技术（如屏幕阅读器）无法解读值无效的 ARIA 属性。[详细了解 ARIA 属性的有效值](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr-value)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "aria-valid-attr": {"id": "aria-valid-attr", "title": "`[aria-*]` 属性有效且拼写正确", "description": "辅助技术（如屏幕阅读器）无法解读名称无效的 ARIA 属性。[详细了解有效的 ARIA 属性](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "button-name": {"id": "button-name", "title": "按钮有可供访问的名称", "description": "当某个按钮没有无障碍名称时，屏幕阅读器会将它读为“按钮”，这会导致依赖屏幕阅读器的用户无法使用它。[了解如何让按钮更便于使用](https://dequeuniversity.com/rules/axe/4.10/button-name)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "bypass": {"id": "bypass", "title": "该页面包含一个标题、跳过链接或地标区域", "description": "添加用于绕过重复内容的方式有助于键盘用户更高效地浏览页面。[详细了解如何绕过内容块](https://dequeuniversity.com/rules/axe/4.10/bypass)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "color-contrast": {"id": "color-contrast", "title": "背景色和前景色具有足够高的对比度", "description": "对于许多用户而言，对比度低的文字都是难以阅读或无法阅读的。[了解如何提供足够高的色彩对比度](https://dequeuniversity.com/rules/axe/4.10/color-contrast)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "definition-list": {"id": "definition-list", "title": "`<dl>` 只包含经过适当排序的 `<dt>` 和 `<dd>` 组及 `<script>`、`<template>` 或 `<div>` 元素。", "description": "如果未正确标记定义列表，屏幕阅读器读出的内容可能会令人困惑或不准确。[了解如何正确构建定义列表](https://dequeuniversity.com/rules/axe/4.10/definition-list)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "dlitem": {"id": "dlitem", "title": "定义列表项已封装在 `<dl>` 元素中", "description": "定义列表项（`<dt>` 和 `<dd>`）必须封装在父 `<dl>` 元素中，以确保屏幕阅读器可以正确地读出它们。[了解如何正确构建定义列表](https://dequeuniversity.com/rules/axe/4.10/dlitem)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "document-title": {"id": "document-title", "title": "文档包含 `<title>` 元素", "description": "屏幕阅读器用户可借助标题来大致了解某个页面的内容，搜索引擎用户则非常依赖标题来确定某个页面是否与其搜索相关。[详细了解文档标题](https://dequeuniversity.com/rules/axe/4.10/document-title)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "duplicate-id-aria": {"id": "duplicate-id-aria", "title": "ARIA ID 都是独一无二的", "description": "ARIA ID 的值必须独一无二，才能防止其他实例被辅助技术忽略。[了解如何修正重复的 ARIA ID](https://dequeuniversity.com/rules/axe/4.10/duplicate-id-aria)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "empty-heading": {"id": "empty-heading", "title": "所有标题元素均包含内容。", "description": "如果标题元素未包含任何内容或包含无法读取的文本，屏幕阅读器用户将难以获取网页的结构信息。[详细了解标题](https://dequeuniversity.com/rules/axe/4.10/empty-heading)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "form-field-multiple-labels": {"id": "form-field-multiple-labels", "title": "表单字段都没有多个标签", "description": "有多个标签的表单字段可能会被辅助技术（如屏幕阅读器）以令人困惑的方式播报出来，因为这些辅助技术可能会使用第一个标签或最后一个标签，也可能会使用所有标签。[了解如何使用表单标签](https://dequeuniversity.com/rules/axe/4.10/form-field-multiple-labels)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "frame-title": {"id": "frame-title", "title": "`<frame>` 或 `<iframe>` 元素有标题", "description": "屏幕阅读器用户依靠框架标题来描述框架的内容。[详细了解框架标题](https://dequeuniversity.com/rules/axe/4.10/frame-title)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "heading-order": {"id": "heading-order", "title": "标题元素按降序显示", "description": "未跳过任何等级且排序正确的标题会准确传达页面的语义结构，从而使得相应页面在使用辅助技术时更易于浏览和理解。[详细了解标题顺序](https://dequeuniversity.com/rules/axe/4.10/heading-order)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "html-has-lang": {"id": "html-has-lang", "title": "`<html>` 元素包含 `[lang]` 属性", "description": "如果页面未指定 `lang` 属性，屏幕阅读器便会假定该页面采用的是用户在设置屏幕阅读器时选择的默认语言。如果该页面实际上并未采用默认语言，屏幕阅读器可能无法正确读出该页面中的文字。[详细了解 `lang` 属性](https://dequeuniversity.com/rules/axe/4.10/html-has-lang)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "html-lang-valid": {"id": "html-lang-valid", "title": "`<html>` 元素的 `[lang]` 属性具备有效值", "description": "指定有效的 [BCP 47 语言](https://www.w3.org/International/questions/qa-choosing-language-tags#question)有助于屏幕阅读器正确读出文字。[了解如何使用 `lang` 属性](https://dequeuniversity.com/rules/axe/4.10/html-lang-valid)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "html-xml-lang-mismatch": {"id": "html-xml-lang-mismatch", "title": "`<html>` 元素的 `[xml:lang]` 属性与 `[lang]` 属性使用了相同的基本语言。", "description": "如果网页未指定一致的语言，屏幕阅读器可能无法正确读出相应网页上的文本。[详细了解 `lang` 属性](https://dequeuniversity.com/rules/axe/4.10/html-xml-lang-mismatch)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "identical-links-same-purpose": {"id": "identical-links-same-purpose", "title": "相同链接的用途一致。", "description": "指向相同目标位置的链接应具有相同的说明，这有助于用户了解链接的用途并决定是否要访问它。[详细了解相同链接](https://dequeuniversity.com/rules/axe/4.10/identical-links-same-purpose)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "image-alt": {"id": "image-alt", "title": "图片元素具备 `[alt]` 属性", "description": "说明性元素应力求使用简短的描述性替代文字。未指定 alt 属性的装饰性元素可被忽略。[详细了解 `alt` 属性](https://dequeuniversity.com/rules/axe/4.10/image-alt)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "image-redundant-alt": {"id": "image-redundant-alt", "title": "图片元素没有属于多余文本的 `[alt]` 属性。", "description": "说明性元素应力求使用简短的描述性替代文本。如果替代文本与链接或图片旁边的文本完全相同，可能会让屏幕阅读器用户感到困惑，因为他们会听到两次同样的内容。[详细了解 `alt` 属性](https://dequeuniversity.com/rules/axe/4.10/image-redundant-alt)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "input-button-name": {"id": "input-button-name", "title": "输入按钮具有可识别的文本。", "description": "向输入按钮添加可识别的无障碍文本可帮助屏幕阅读器用户了解输入按钮的用途。[详细了解输入按钮](https://dequeuniversity.com/rules/axe/4.10/input-button-name)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "input-image-alt": {"id": "input-image-alt", "title": "`<input type=\"image\">` 元素有对应的 `[alt]` 文字", "description": "将图片用作 `<input>` 按钮时，提供替代文字有助于屏幕阅读器用户了解该按钮的用途。[了解输入图片替代文字](https://dequeuniversity.com/rules/axe/4.10/input-image-alt)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "label-content-name-mismatch": {"id": "label-content-name-mismatch", "title": "带有可见文本标签的元素具有匹配的无障碍名称。", "description": "如果可见文本标签与无障碍名称不匹配，可能会让屏幕阅读器用户感到困惑。[详细了解无障碍名称](https://dequeuniversity.com/rules/axe/4.10/label-content-name-mismatch)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "label": {"id": "label", "title": "表单元素具有关联的标签", "description": "标签可确保辅助技术（如屏幕阅读器）正确读出表单控件。[详细了解表单元素标签](https://dequeuniversity.com/rules/axe/4.10/label)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "landmark-one-main": {"id": "landmark-one-main", "title": "文档有一个主要位置标记。", "description": "主要位置标记有助于屏幕阅读器用户浏览网页。[详细了解位置标记](https://dequeuniversity.com/rules/axe/4.10/landmark-one-main)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "link-name": {"id": "link-name", "title": "链接具备可识别的名称", "description": "请确保链接文字（以及用作链接的图片替代文字）可识别、独一无二且可聚焦，这样做会提升屏幕阅读器用户的导航体验。[了解如何使链接可供访问](https://dequeuniversity.com/rules/axe/4.10/link-name)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "link-in-text-block": {"id": "link-in-text-block", "title": "无需依赖颜色即可辨识链接。", "description": "对许多用户而言，对比度低的文本都是难以阅读或无法阅读的。清晰可辨的链接文本可改善低视力用户的体验。[了解如何让链接容易辨识](https://dequeuniversity.com/rules/axe/4.10/link-in-text-block)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "list": {"id": "list", "title": "列表只包含 `<li>` 元素和脚本支持元素（`<script>` 和 `<template>`）。", "description": "屏幕阅读器会采用特定的方法来读出列表内容。确保列表结构正确有助于屏幕阅读器顺利读出相应内容。[详细了解适当的列表结构](https://dequeuniversity.com/rules/axe/4.10/list)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "listitem": {"id": "listitem", "title": "列表项 (`<li>`) 包含在 `<ul>`、`<ol>` 或 `<menu>` 父元素中", "description": "屏幕阅读器要求列表项 (`<li>`) 必须包含在父 `<ul>`、`<ol>` 或 `<menu>` 中，这样才能正确读出它们。[详细了解适当的列表结构](https://dequeuniversity.com/rules/axe/4.10/listitem)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "meta-refresh": {"id": "meta-refresh", "title": "文档未使用 `<meta http-equiv=\"refresh\">`", "description": "用户并不希望网页自动刷新，因为自动刷新会不断地将焦点移回到页面顶部。这可能会让用户感到沮丧或困惑。[详细了解刷新元标记](https://dequeuniversity.com/rules/axe/4.10/meta-refresh)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "meta-viewport": {"id": "meta-viewport", "title": "`[user-scalable=\"no\"]` 未用在 `<meta name=\"viewport\">` 元素中，并且 `[maximum-scale]` 属性不小于 5。", "description": "对于必须依靠放大屏幕才能清晰看到网页内容的低视力用户而言，停用缩放功能会给他们带来问题。[详细了解视口元标记](https://dequeuniversity.com/rules/axe/4.10/meta-viewport)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "object-alt": {"id": "object-alt", "title": "`<object>` 元素有对应的替代文字", "description": "屏幕阅读器无法转换非文字内容。通过向 `<object>` 元素添加替代文字，可帮助屏幕阅读器将含义传达给用户。[详细了解 `object` 元素的替代文字](https://dequeuniversity.com/rules/axe/4.10/object-alt)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "select-name": {"id": "select-name", "title": "select 元素具有关联的标签元素。", "description": "如果表单元素缺少有效标签，可能会给屏幕阅读器用户带来糟糕的体验。[详细了解 `select` 元素](https://dequeuniversity.com/rules/axe/4.10/select-name)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "skip-link": {"id": "skip-link", "title": "跳转链接可聚焦。", "description": "添加跳转链接可帮助用户跳至主要内容以节省时间。[详细了解跳转链接](https://dequeuniversity.com/rules/axe/4.10/skip-link)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "tabindex": {"id": "tabindex", "title": "所有元素的 `[tabindex]` 值都不大于 0", "description": "值大于 0 意味着明确的导航顺序。尽管这在技术上可行，但往往会让依赖辅助技术的用户感到沮丧。[详细了解 `tabindex` 属性](https://dequeuniversity.com/rules/axe/4.10/tabindex)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "table-duplicate-name": {"id": "table-duplicate-name", "title": "相关表格的 summary 属性和 `<caption>` 具有不同的内容。", "description": "summary 属性应描述表格结构，而 `<caption>` 应含有屏幕上显示的标题。准确的表格标记对屏幕阅读器用户有帮助。[详细了解 summary 和 caption](https://dequeuniversity.com/rules/axe/4.10/table-duplicate-name)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "table-fake-caption": {"id": "table-fake-caption", "title": "表格使用了 `<caption>`（而非带有 `[colspan]` 属性的单元格）来表示表格标题。", "description": "屏幕阅读器提供了更便于用户浏览表格内容的功能。请务必确保表格实际使用了 <caption> 元素（而非带有 `[colspan]` 属性的单元格），这可以提升屏幕阅读器用户的体验。[详细了解表格标题](https://dequeuniversity.com/rules/axe/4.10/table-fake-caption)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "target-size": {"id": "target-size", "title": "触摸目标的尺寸和间距足够大。", "description": "如果触摸目标的尺寸和间距足够大，就能帮助难以精准触按小控件的用户激活相应目标。[详细了解触摸目标](https://dequeuniversity.com/rules/axe/4.10/target-size)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "td-has-header": {"id": "td-has-header", "title": "大型 `<table>` 中的 `<td>` 元素具有一个或多个表格标头。", "description": "屏幕阅读器提供了更便于用户浏览表格内容的功能。请务必确保大型表格（宽度和高度至少为 3 个单元格）中的 `<td>` 元素具有关联的表格标头，这可以提升屏幕阅读器用户的体验。[详细了解表格标头](https://dequeuniversity.com/rules/axe/4.10/td-has-header)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "td-headers-attr": {"id": "td-headers-attr", "title": "`<table>` 元素中使用 `[headers]` 属性的单元格引用的是同一表格中的单元格。", "description": "屏幕阅读器提供了更便于用户浏览表格内容的功能。请确保那些使用 `[headers]` 属性的 `<td>` 单元格仅引用同一个表格中的其他单元格，这样做可提升屏幕阅读器用户的体验。[详细了解 `headers` 属性](https://dequeuniversity.com/rules/axe/4.10/td-headers-attr)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "th-has-data-cells": {"id": "th-has-data-cells", "title": "`<th>` 元素和 `[role=\"columnheader\"/\"rowheader\"]` 的元素具备它们所描述的数据单元格。", "description": "屏幕阅读器提供了更便于用户浏览表格内容的功能。确保表格标头始终引用特定一组单元格可以提升屏幕阅读器用户的体验。[详细了解表格标头](https://dequeuniversity.com/rules/axe/4.10/th-has-data-cells)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "valid-lang": {"id": "valid-lang", "title": "`[lang]` 属性的值有效", "description": "为元素指定有效的 [BCP 47 语言](https://www.w3.org/International/questions/qa-choosing-language-tags#question)有助于确保屏幕阅读器正确读出文字。[了解如何使用 `lang` 属性](https://dequeuniversity.com/rules/axe/4.10/valid-lang)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "video-caption": {"id": "video-caption", "title": "`<video>` 元素包含带 `[kind=\"captions\"]` 的 `<track>` 元素", "description": "如果视频提供了字幕，失聪用户和听障用户便能更轻松地获取此视频中的信息。[详细了解视频字幕](https://dequeuniversity.com/rules/axe/4.10/video-caption)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "custom-controls-labels": {"id": "custom-controls-labels", "title": "Custom controls have associated labels", "description": "Custom interactive controls have associated labels, provided by aria-label or aria-labelledby. [Learn more about custom controls and labels](https://developer.chrome.com/docs/lighthouse/accessibility/custom-controls-labels/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "custom-controls-roles": {"id": "custom-controls-roles", "title": "Custom controls have ARIA roles", "description": "Custom interactive controls have appropriate ARIA roles. [Learn how to add roles to custom controls](https://developer.chrome.com/docs/lighthouse/accessibility/custom-control-roles/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "focus-traps": {"id": "focus-traps", "title": "User focus is not accidentally trapped in a region", "description": "A user can tab into and out of any control or region without accidentally trapping their focus. [Learn how to avoid focus traps](https://developer.chrome.com/docs/lighthouse/accessibility/focus-traps/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "focusable-controls": {"id": "focusable-controls", "title": "Interactive controls are keyboard focusable", "description": "Custom interactive controls are keyboard focusable and display a focus indicator. [Learn how to make custom controls focusable](https://developer.chrome.com/docs/lighthouse/accessibility/focusable-controls/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "interactive-element-affordance": {"id": "interactive-element-affordance", "title": "Interactive elements indicate their purpose and state", "description": "Interactive elements, such as links and buttons, should indicate their state and be distinguishable from non-interactive elements. [Learn how to decorate interactive elements with affordance hints](https://developer.chrome.com/docs/lighthouse/accessibility/interactive-element-affordance/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "logical-tab-order": {"id": "logical-tab-order", "title": "The page has a logical tab order", "description": "Tabbing through the page follows the visual layout. Users cannot focus elements that are offscreen. [Learn more about logical tab ordering](https://developer.chrome.com/docs/lighthouse/accessibility/logical-tab-order/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "managed-focus": {"id": "managed-focus", "title": "The user's focus is directed to new content added to the page", "description": "If new content, such as a dialog, is added to the page, the user's focus is directed to it. [Learn how to direct focus to new content](https://developer.chrome.com/docs/lighthouse/accessibility/managed-focus/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "offscreen-content-hidden": {"id": "offscreen-content-hidden", "title": "Offscreen content is hidden from assistive technology", "description": "Offscreen content is hidden with display: none or aria-hidden=true. [Learn how to properly hide offscreen content](https://developer.chrome.com/docs/lighthouse/accessibility/offscreen-content-hidden/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "use-landmarks": {"id": "use-landmarks", "title": "HTML5 landmark elements are used to improve navigation", "description": "Landmark elements (`<main>`, `<nav>`, etc.) are used to improve the keyboard navigation of the page for assistive technology. [Learn more about landmark elements](https://developer.chrome.com/docs/lighthouse/accessibility/use-landmarks/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "visual-order-follows-dom": {"id": "visual-order-follows-dom", "title": "Visual order on the page follows DOM order", "description": "DOM order matches the visual order, improving navigation for assistive technology. [Learn more about DOM and visual ordering](https://developer.chrome.com/docs/lighthouse/accessibility/visual-order-follows-dom/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "uses-long-cache-ttl": {"id": "uses-long-cache-ttl", "title": "针对静态资源使用高效的缓存策略", "description": "延长缓存期限可加快重访您网页的速度。[详细了解高效缓存政策](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3}, "total-byte-weight": {"id": "total-byte-weight", "title": "避免网络负载过大", "description": "网络载荷过大不仅会让用户付出真金白银，还极有可能会延长加载用时。[了解如何缩减载荷大小](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 1}, "offscreen-images": {"id": "offscreen-images", "title": "推迟加载屏幕外图片", "description": "建议您在所有关键资源加载完毕后再加载屏幕外图片和处于隐藏状态的图片，从而缩短 Time to Interactive。[了解如何推迟加载屏幕外图片](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 2}, "render-blocking-resources": {"id": "render-blocking-resources", "title": "移除阻塞渲染的资源", "description": "资源阻止了系统对您网页的首次绘制。建议以内嵌方式提供关键的 JS/CSS，并推迟提供所有非关键的 JS/样式。[了解如何移除阻塞渲染的资源](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 2}, "unminified-css": {"id": "unminified-css", "title": "缩减 CSS", "description": "缩减 CSS 文件大小可缩减网络载荷规模。[了解如何缩减 CSS 的大小](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3}, "unminified-javascript": {"id": "unminified-javascript", "title": "缩减 JavaScript", "description": "缩减 JavaScript 文件大小不仅可以缩减载荷规模，还能缩短脚本解析用时。[了解如何缩减 JavaScript 的大小](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3}, "unused-css-rules": {"id": "unused-css-rules", "title": "减少未使用的 CSS", "description": "请从样式表中减少未使用的规则，并延迟加载首屏内容未用到的 CSS，以减少网络活动耗用的字节数。[了解如何减少未使用的 CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 1}, "unused-javascript": {"id": "unused-javascript", "title": "减少未使用的 JavaScript", "description": "请减少未使用的 JavaScript，并等到需要使用时再加载脚本，以减少网络活动耗用的字节数。[了解如何减少未使用的 JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 1}, "modern-image-formats": {"id": "modern-image-formats", "title": "采用新一代格式提供图片", "description": "WebP 和 AVIF 等图片格式的压缩效果通常优于 PNG 或 JPEG，因而下载速度更快，消耗的数据流量更少。[详细了解现代图片格式](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3}, "uses-optimized-images": {"id": "uses-optimized-images", "title": "对图片进行高效编码", "description": "经过优化的图片有助于提升加载速度，并减少消耗的移动数据网络流量。[了解如何高效地对图片进行编码](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 2}, "uses-text-compression": {"id": "uses-text-compression", "title": "启用文本压缩", "description": "对于文本资源，应先压缩（gzip、deflate 或 brotli），然后再提供，以最大限度地减少网络活动消耗的字节总数。[详细了解文本压缩](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3}, "uses-responsive-images": {"id": "uses-responsive-images", "title": "适当调整图片大小", "description": "提供大小合适的图片可节省移动数据网络流量并缩短加载用时。[了解如何调整图片大小](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 2}, "efficient-animated-content": {"id": "efficient-animated-content", "title": "使用视频格式提供动画内容", "description": "使用大型 GIF 提供动画内容会导致效率低下。建议您改用 MPEG4/WebM 视频（来提供动画）和 PNG/WebP（来提供静态图片）以减少网络活动消耗的字节数。[详细了解高效视频格式](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3}, "duplicated-javascript": {"id": "duplicated-javascript", "title": "请移除 JavaScript 软件包中的重复模块", "description": "从软件包中移除重复的大型 JavaScript 模块可减少网络传输时不必要的流量消耗。 ", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 2}, "legacy-javascript": {"id": "legacy-javascript", "title": "应避免向新型浏览器提供旧版 JavaScript", "description": "Polyfill 和 transform 让旧版浏览器能够使用新的 JavaScript 功能。不过，很多功能对现代浏览器而言并不是必需的。请考虑修改 JavaScript 构建流程，以便不转译 [Baseline](https://web.dev/baseline) 功能，除非您知道必须支持旧版浏览器。[了解为什么大多数网站可以部署 ES6+ 代码而无需转译](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 2}, "doctype": {"id": "doctype", "title": "页面包含 HTML DOCTYPE", "description": "指定 DOCTYPE 可阻止浏览器切换到 Quirks 模式。[详细了解 DOCTYPE 声明](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "charset": {"id": "charset", "title": "正确地设定了字符集", "description": "必须声明字符编码。您可以在 HTML 的前 1024 个字节中使用 `<meta>` 标记来声明，也可以在 HTTP 响应标头 Content-Type 中进行声明。[详细了解如何声明字符编码](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "dom-size": {"id": "dom-size", "title": "避免 DOM 规模过大", "description": "大型 DOM 会增加内存用量、导致[样式计算](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations)用时延长，并产生高昂的[布局自动重排](https://developers.google.com/speed/articles/reflow)成本。[了解如何避免 DOM 规模过大](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 1}, "geolocation-on-start": {"id": "geolocation-on-start", "title": "避免在网页加载时请求地理定位权限", "description": "如果网站在缺少上下文的情况下请求位置信息，会导致用户不信任网站或感到困惑。建议将请求与用户操作进行绑定。[详细了解地理定位权限](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "inspector-issues": {"id": "inspector-issues", "title": "Chrome Devtools 的“`Issues`”面板中无任何问题", "description": "Chrome Devtools 的“`Issues`”面板中记录的问题表明有未解决的问题。这些问题的起因可能是网络请求失败、安全控件不足和其他浏览器问题。如需详细了解每个问题，请打开 Chrome Devtools 的“Issues”面板。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "no-document-write": {"id": "no-document-write", "title": "请勿使用 `document.write()`", "description": "对于连接速度较慢的用户，通过 `document.write()` 动态注入的外部脚本可将网页加载延迟数十秒。[了解如何避免使用 document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 2}, "js-libraries": {"id": "js-libraries", "title": "已检测到的 JavaScript 库", "description": "页面上检测到的所有前端 JavaScript 库。[详细了解此 JavaScript 库检测诊断审核](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "notification-on-start": {"id": "notification-on-start", "title": "避免在网页加载时请求通知权限", "description": "如果网站在缺少上下文的情况下请求发送通知，会导致用户不信任网站或感到困惑。建议将请求与用户手势进行绑定。[详细了解如何以负责任的方式获取通知权限](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "paste-preventing-inputs": {"id": "paste-preventing-inputs", "title": "允许用户将内容粘贴到输入字段中", "description": "禁用粘贴式输入功能会对用户体验产生不良影响，停用密码管理工具则会降低安全性。[详细了解方便用户使用的输入字段](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "uses-http2": {"id": "uses-http2", "title": "使用 HTTP/2", "description": "HTTP/2 提供了许多优于 HTTP/1.1 的益处，包括二进制标头和多路复用。[详细了解 HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3}, "uses-passive-event-listeners": {"id": "uses-passive-event-listeners", "title": "使用被动式监听器来提高滚动性能", "description": "建议您将轻触和滚轮事件监听器标记为 `passive`，以提高页面的滚动性能。[详细了解如何采用被动事件监听器](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3}, "meta-description": {"id": "meta-description", "title": "文档有 meta 描述", "description": "元描述可能会被包含在搜索结果中，以简要概括网页内容。[详细了解元描述](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "http-status-code": {"id": "http-status-code", "title": "页面返回了有效的 HTTP 状态代码", "description": "返回无效 HTTP 状态代码的页面可能无法被正确编入索引。[详细了解 HTTP 状态代码](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "font-size": {"id": "font-size", "title": "文档所用的字体大小清晰可辨", "description": "12px 以下的字体过小，会导致用户无法辨认；此外，这样的字体需要移动设备访问者“张合双指进行缩放”才能阅读。请尽量让 60% 以上的页面文字不小于 12px。[详细了解清晰可辨的字体大小](https://developer.chrome.com/docs/lighthouse/seo/font-size/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "link-text": {"id": "link-text", "title": "链接有描述性文字", "description": "描述性链接文字有助于搜索引擎理解您的内容。[了解如何让链接更便于使用](https://developer.chrome.com/docs/lighthouse/seo/link-text/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "crawlable-anchors": {"id": "crawlable-anchors", "title": "链接都是可抓取的", "description": "搜索引擎可能会使用链接中的 `href` 属性来抓取网站。请确保锚元素的 `href` 属性链接到合适的目标网站，以便搜索引擎发现该网站上的更多网页。[了解如何使链接可供抓取](https://support.google.com/webmasters/answer/9112205)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "is-crawlable": {"id": "is-crawlable", "title": "页面未被屏蔽，可编入索引", "description": "如果搜索引擎无权抓取您的网页，则无法将它们添加到搜索结果中。[详细了解抓取工具指令](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "robots-txt": {"id": "robots-txt", "title": "robots.txt 有效", "description": "如果 robots.txt 文件的格式不正确，抓取工具可能无法理解您希望以何种方式抓取网站内容或将其编入索引。[详细了解 robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "hreflang": {"id": "hreflang", "title": "文档的 `hreflang` 有效", "description": "hreflang 链接用于告知搜索引擎应在特定语言或地区的搜索结果中显示哪种版本的网页。[详细了解 `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "canonical": {"id": "canonical", "title": "文档的 `rel=canonical` 有效", "description": "规范链接用于建议应在搜索结果中显示哪个网址。[详细了解规范链接](https://developer.chrome.com/docs/lighthouse/seo/canonical/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "structured-data": {"id": "structured-data", "title": "结构化数据有效", "description": "运行[结构化数据测试工具](https://search.google.com/structured-data/testing-tool/)和 [Structured Data Linter](http://linter.structured-data.org/) 可验证结构化数据。[详细了解结构化数据](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531"}, "bf-cache": {"id": "bf-cache", "title": "网页并未阻止恢复往返缓存", "description": "许多导航操作都是网页间来回切换，往返缓存 (bfcache) 可以加快这些返回导航的速度。[详细了解 bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 4}, "cache-insight": {"id": "cache-insight", "title": "使用高效的缓存生命周期", "description": "延长缓存生命周期可加快重访您网页的速度。[了解详情](https://web.dev/uses-long-cache-ttl/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3, "replacesAudits": ["uses-long-cache-ttl"]}, "cls-culprits-insight": {"id": "cls-culprits-insight", "title": "布局偏移原因", "description": "当元素在没有任何用户互动的情况下移动时，就会发生布局偏移。[调查布局偏移的原因](https://web.dev/articles/optimize-cls)，例如在网页加载时添加、移除元素或元素字体发生了变化。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3, "replacesAudits": ["layout-shifts", "non-composited-animations", "unsized-images"]}, "document-latency-insight": {"id": "document-latency-insight", "title": "文档请求延迟", "description": "您的第一个网络请求最为重要。您可通过避免重定向、确保服务器快速响应以及启用文本压缩，缩短其延迟时间。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3, "replacesAudits": ["redirects", "server-response-time", "uses-text-compression"]}, "dom-size-insight": {"id": "dom-size-insight", "title": "优化 DOM 大小", "description": "大型 DOM 可能会增加样式计算和布局自动重排的用时，从而影响网页响应速度。大型 DOM 也会增加内存用量。[了解如何避免 DOM 规模过大](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3, "replacesAudits": ["dom-size"]}, "duplicated-javascript-insight": {"id": "duplicated-javascript-insight", "title": "重复的 JavaScript", "description": "从软件包中移除重复的大型 JavaScript 模块，即可避免网络活动带来不必要的字节消耗。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 2, "replacesAudits": ["duplicated-javascript"]}, "font-display-insight": {"id": "font-display-insight", "title": "字体显示", "description": "建议您将 [font-display](https://developer.chrome.com/blog/font-display) 设为 swap 或 optional，确保文本始终可见。通过[替换字体指标](https://developer.chrome.com/blog/font-fallbacks)可进一步优化 swap，缓解布局偏移。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3, "replacesAudits": ["font-display"]}, "forced-reflow-insight": {"id": "forced-reflow-insight", "title": "强制自动重排", "description": "许多 API（通常用于读取布局几何图形信息）会强制渲染引擎暂停脚本执行，以便计算样式和布局。详细了解[强制自动重排](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts)及其缓解措施。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3}, "image-delivery-insight": {"id": "image-delivery-insight", "title": "改进图片传送", "description": "缩短图片下载时间可优化网页的感知加载时间和 LCP。[详细了解图片大小优化](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3, "replacesAudits": ["modern-image-formats", "uses-optimized-images", "efficient-animated-content", "uses-responsive-images"]}, "interaction-to-next-paint-insight": {"id": "interaction-to-next-paint-insight", "title": "按阶段划分的 INP", "description": "请从持续时间最长的阶段开始检查。[几处延迟可降低到最小](https://web.dev/articles/optimize-inp#optimize_interactions)。如需缩短处理时长，请[优化主线程（通常是 JS）成本](https://web.dev/articles/optimize-long-tasks)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3, "replacesAudits": ["work-during-interaction"]}, "lcp-discovery-insight": {"id": "lcp-discovery-insight", "title": "发现 LCP 请求", "description": "使 LCP 图像能够立即从 HTML 中[被发现](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay)，并[避免延迟加载](https://web.dev/articles/lcp-lazy-loading)，以此优化 LCP", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3, "replacesAudits": ["prioritize-lcp-image", "lcp-lazy-loaded"]}, "lcp-phases-insight": {"id": "lcp-phases-insight", "title": "按阶段划分的 LCP", "description": "每个[阶段都有特定的改进策略](https://web.dev/articles/optimize-lcp#lcp-breakdown)。理想情况下，大部分 LCP 时间应该花在加载资源上，而不是浪费在延迟上。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3, "replacesAudits": ["largest-contentful-paint-element"]}, "legacy-javascript-insight": {"id": "legacy-javascript-insight", "title": "旧版 JavaScript", "description": "polyfill 和 transform 让旧版浏览器能够使用新的 JavaScript 功能。不过，很多功能对现代浏览器而言并不是必需的。请考虑修改 JavaScript 构建流程，以便不转译 [Baseline](https://web.dev/articles/baseline-and-polyfills) 功能，除非您知道必须支持旧版浏览器。[了解为什么大多数网站可以部署 ES6+ 代码而无需转译](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 2}, "modern-http-insight": {"id": "modern-http-insight", "title": "现代 HTTP", "description": "HTTP/2 和 HTTP/3 拥有许多 HTTP/1.1 没有的优势，例如多路复用。[详细了解如何使用现代 HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3}, "network-dependency-tree-insight": {"id": "network-dependency-tree-insight", "title": "网络依赖关系树", "description": "[避免链接关键请求](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains)，方法是缩短链长、缩减资源的下载文件大小，或者推迟下载不必要的资源，从而提高网页加载速度。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 1, "replacesAudits": ["critical-request-chains"]}, "render-blocking-insight": {"id": "render-blocking-insight", "title": "渲染屏蔽请求", "description": "请求正在屏蔽网页的初始渲染，这可能会延迟 LCP。[延迟或内嵌](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources/)可以将这些网络请求移出关键路径。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3, "replacesAudits": ["render-blocking-resources"]}, "third-parties-insight": {"id": "third-parties-insight", "title": "第三方", "description": "第三方代码可能会显著影响加载性能。[请减少并推迟加载第三方代码](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)，以优先渲染您的网页内容。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3, "replacesAudits": ["third-party-summary"]}, "viewport-insight": {"id": "viewport-insight", "title": "针对移动设备优化视口", "description": "如果视口未针对移动设备进行优化，点按互动可能会[延迟最多 300 毫秒](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)。", "score": null, "scoreDisplayMode": "error", "errorMessage": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。(NO_FCP)", "errorStack": "LighthouseError: NO_FCP\n    at devtools://devtools/bundled/third_party/lighthouse/lighthouse-dt-bundle.js:1786:531", "guidanceLevel": 3, "replacesAudits": ["viewport"]}}, "configSettings": {"output": "json", "maxWaitForFcp": 30000, "maxWaitForLoad": 45000, "pauseAfterFcpMs": 1000, "pauseAfterLoadMs": 1000, "networkQuietThresholdMs": 1000, "cpuQuietThresholdMs": 1000, "formFactor": "desktop", "throttling": {"rttMs": 40, "throughputKbps": 10240, "requestLatencyMs": 0, "downloadThroughputKbps": 0, "uploadThroughputKbps": 0, "cpuSlowdownMultiplier": 1}, "throttlingMethod": "simulate", "screenEmulation": {"mobile": true, "width": 412, "height": 823, "deviceScaleFactor": 1.75, "disabled": true}, "emulatedUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "auditMode": false, "gatherMode": false, "clearStorageTypes": ["file_systems", "shader_cache", "service_workers", "cache_storage"], "disableStorageReset": false, "debugNavigation": false, "channel": "devtools", "usePassiveGathering": false, "disableFullPageScreenshot": false, "skipAboutBlank": false, "blankPage": "about:blank", "ignoreStatusCode": true, "locale": "zh", "blockedUrlPatterns": null, "additionalTraceCategories": "", "extraHeaders": null, "precomputedLanternData": null, "onlyAudits": null, "onlyCategories": ["performance", "accessibility", "best-practices", "seo"], "skipAudits": null}, "categories": {"performance": {"title": "性能", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "first-contentful-paint", "weight": 10, "group": "metrics", "acronym": "FCP"}, {"id": "largest-contentful-paint", "weight": 25, "group": "metrics", "acronym": "LCP"}, {"id": "total-blocking-time", "weight": 30, "group": "metrics", "acronym": "TBT"}, {"id": "cumulative-layout-shift", "weight": 25, "group": "metrics", "acronym": "CLS"}, {"id": "speed-index", "weight": 10, "group": "metrics", "acronym": "SI"}, {"id": "cache-insight", "weight": 0, "group": "hidden"}, {"id": "cls-culprits-insight", "weight": 0, "group": "hidden"}, {"id": "document-latency-insight", "weight": 0, "group": "hidden"}, {"id": "dom-size-insight", "weight": 0, "group": "hidden"}, {"id": "duplicated-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "font-display-insight", "weight": 0, "group": "hidden"}, {"id": "forced-reflow-insight", "weight": 0, "group": "hidden"}, {"id": "image-delivery-insight", "weight": 0, "group": "hidden"}, {"id": "interaction-to-next-paint-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-discovery-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-phases-insight", "weight": 0, "group": "hidden"}, {"id": "legacy-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "modern-http-insight", "weight": 0, "group": "hidden"}, {"id": "network-dependency-tree-insight", "weight": 0, "group": "hidden"}, {"id": "render-blocking-insight", "weight": 0, "group": "hidden"}, {"id": "third-parties-insight", "weight": 0, "group": "hidden"}, {"id": "viewport-insight", "weight": 0, "group": "hidden"}, {"id": "interactive", "weight": 0, "group": "hidden", "acronym": "TTI"}, {"id": "max-potential-fid", "weight": 0, "group": "hidden"}, {"id": "first-meaningful-paint", "weight": 0, "acronym": "FMP", "group": "hidden"}, {"id": "render-blocking-resources", "weight": 0, "group": "diagnostics"}, {"id": "uses-responsive-images", "weight": 0, "group": "diagnostics"}, {"id": "offscreen-images", "weight": 0, "group": "diagnostics"}, {"id": "unminified-css", "weight": 0, "group": "diagnostics"}, {"id": "unminified-javascript", "weight": 0, "group": "diagnostics"}, {"id": "unused-css-rules", "weight": 0, "group": "diagnostics"}, {"id": "unused-javascript", "weight": 0, "group": "diagnostics"}, {"id": "uses-optimized-images", "weight": 0, "group": "diagnostics"}, {"id": "modern-image-formats", "weight": 0, "group": "diagnostics"}, {"id": "uses-text-compression", "weight": 0, "group": "diagnostics"}, {"id": "uses-rel-preconnect", "weight": 0, "group": "diagnostics"}, {"id": "server-response-time", "weight": 0, "group": "diagnostics"}, {"id": "redirects", "weight": 0, "group": "diagnostics"}, {"id": "uses-http2", "weight": 0, "group": "diagnostics"}, {"id": "efficient-animated-content", "weight": 0, "group": "diagnostics"}, {"id": "duplicated-javascript", "weight": 0, "group": "diagnostics"}, {"id": "legacy-javascript", "weight": 0, "group": "diagnostics"}, {"id": "prioritize-lcp-image", "weight": 0, "group": "diagnostics"}, {"id": "total-byte-weight", "weight": 0, "group": "diagnostics"}, {"id": "uses-long-cache-ttl", "weight": 0, "group": "diagnostics"}, {"id": "dom-size", "weight": 0, "group": "diagnostics"}, {"id": "critical-request-chains", "weight": 0, "group": "diagnostics"}, {"id": "user-timings", "weight": 0, "group": "diagnostics"}, {"id": "bootup-time", "weight": 0, "group": "diagnostics"}, {"id": "mainthread-work-breakdown", "weight": 0, "group": "diagnostics"}, {"id": "font-display", "weight": 0, "group": "diagnostics"}, {"id": "third-party-summary", "weight": 0, "group": "diagnostics"}, {"id": "third-party-facades", "weight": 0, "group": "diagnostics"}, {"id": "largest-contentful-paint-element", "weight": 0, "group": "diagnostics"}, {"id": "lcp-lazy-loaded", "weight": 0, "group": "diagnostics"}, {"id": "layout-shifts", "weight": 0, "group": "diagnostics"}, {"id": "uses-passive-event-listeners", "weight": 0, "group": "diagnostics"}, {"id": "no-document-write", "weight": 0, "group": "diagnostics"}, {"id": "long-tasks", "weight": 0, "group": "diagnostics"}, {"id": "non-composited-animations", "weight": 0, "group": "diagnostics"}, {"id": "unsized-images", "weight": 0, "group": "diagnostics"}, {"id": "viewport", "weight": 0, "group": "diagnostics"}, {"id": "bf-cache", "weight": 0, "group": "diagnostics"}, {"id": "network-requests", "weight": 0, "group": "hidden"}, {"id": "network-rtt", "weight": 0, "group": "hidden"}, {"id": "network-server-latency", "weight": 0, "group": "hidden"}, {"id": "main-thread-tasks", "weight": 0, "group": "hidden"}, {"id": "diagnostics", "weight": 0, "group": "hidden"}, {"id": "metrics", "weight": 0, "group": "hidden"}, {"id": "screenshot-thumbnails", "weight": 0, "group": "hidden"}, {"id": "final-screenshot", "weight": 0, "group": "hidden"}, {"id": "script-treemap-data", "weight": 0, "group": "hidden"}, {"id": "resource-summary", "weight": 0, "group": "hidden"}], "id": "performance", "score": null}, "accessibility": {"title": "无障碍", "description": "这些检查会突出显示可[改进您 Web 应用的无障碍功能](https://developer.chrome.com/docs/lighthouse/accessibility/)的提示。自动检测功能只能检测到一部分问题，无法保证您 Web 应用的无障碍程度，因此您不妨再[手动测试](https://web.dev/articles/how-to-review)一下。", "manualDescription": "这些条目旨在检查自动化测试工具未涵盖的方面。如需了解详情，请参阅有关如何[执行无障碍功能审查](https://web.dev/articles/how-to-review)的指南。", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "accesskeys", "weight": 7, "group": "a11y-navigation"}, {"id": "aria-allowed-attr", "weight": 10, "group": "a11y-aria"}, {"id": "aria-allowed-role", "weight": 1, "group": "a11y-aria"}, {"id": "aria-command-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-conditional-attr", "weight": 7, "group": "a11y-aria"}, {"id": "aria-deprecated-role", "weight": 1, "group": "a11y-aria"}, {"id": "aria-dialog-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-hidden-body", "weight": 10, "group": "a11y-aria"}, {"id": "aria-hidden-focus", "weight": 7, "group": "a11y-aria"}, {"id": "aria-input-field-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-meter-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-progressbar-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-prohibited-attr", "weight": 7, "group": "a11y-aria"}, {"id": "aria-required-attr", "weight": 10, "group": "a11y-aria"}, {"id": "aria-required-children", "weight": 10, "group": "a11y-aria"}, {"id": "aria-required-parent", "weight": 10, "group": "a11y-aria"}, {"id": "aria-roles", "weight": 7, "group": "a11y-aria"}, {"id": "aria-text", "weight": 7, "group": "a11y-aria"}, {"id": "aria-toggle-field-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-tooltip-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-treeitem-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-valid-attr-value", "weight": 10, "group": "a11y-aria"}, {"id": "aria-valid-attr", "weight": 10, "group": "a11y-aria"}, {"id": "button-name", "weight": 10, "group": "a11y-names-labels"}, {"id": "bypass", "weight": 7, "group": "a11y-navigation"}, {"id": "color-contrast", "weight": 7, "group": "a11y-color-contrast"}, {"id": "definition-list", "weight": 7, "group": "a11y-tables-lists"}, {"id": "dlitem", "weight": 7, "group": "a11y-tables-lists"}, {"id": "document-title", "weight": 7, "group": "a11y-names-labels"}, {"id": "duplicate-id-aria", "weight": 10, "group": "a11y-aria"}, {"id": "form-field-multiple-labels", "weight": 3, "group": "a11y-names-labels"}, {"id": "frame-title", "weight": 7, "group": "a11y-names-labels"}, {"id": "heading-order", "weight": 3, "group": "a11y-navigation"}, {"id": "html-has-lang", "weight": 7, "group": "a11y-language"}, {"id": "html-lang-valid", "weight": 7, "group": "a11y-language"}, {"id": "html-xml-lang-mismatch", "weight": 3, "group": "a11y-language"}, {"id": "image-alt", "weight": 10, "group": "a11y-names-labels"}, {"id": "image-redundant-alt", "weight": 1, "group": "a11y-names-labels"}, {"id": "input-button-name", "weight": 10, "group": "a11y-names-labels"}, {"id": "input-image-alt", "weight": 10, "group": "a11y-names-labels"}, {"id": "label", "weight": 7, "group": "a11y-names-labels"}, {"id": "link-in-text-block", "weight": 7, "group": "a11y-color-contrast"}, {"id": "link-name", "weight": 7, "group": "a11y-names-labels"}, {"id": "list", "weight": 7, "group": "a11y-tables-lists"}, {"id": "listitem", "weight": 7, "group": "a11y-tables-lists"}, {"id": "meta-refresh", "weight": 10, "group": "a11y-best-practices"}, {"id": "meta-viewport", "weight": 10, "group": "a11y-best-practices"}, {"id": "object-alt", "weight": 7, "group": "a11y-names-labels"}, {"id": "select-name", "weight": 7, "group": "a11y-names-labels"}, {"id": "skip-link", "weight": 3, "group": "a11y-names-labels"}, {"id": "tabindex", "weight": 7, "group": "a11y-navigation"}, {"id": "table-duplicate-name", "weight": 1, "group": "a11y-tables-lists"}, {"id": "target-size", "weight": 7, "group": "a11y-best-practices"}, {"id": "td-headers-attr", "weight": 7, "group": "a11y-tables-lists"}, {"id": "th-has-data-cells", "weight": 7, "group": "a11y-tables-lists"}, {"id": "valid-lang", "weight": 7, "group": "a11y-language"}, {"id": "video-caption", "weight": 10, "group": "a11y-audio-video"}, {"id": "focusable-controls", "weight": 0}, {"id": "interactive-element-affordance", "weight": 0}, {"id": "logical-tab-order", "weight": 0}, {"id": "visual-order-follows-dom", "weight": 0}, {"id": "focus-traps", "weight": 0}, {"id": "managed-focus", "weight": 0}, {"id": "use-landmarks", "weight": 0}, {"id": "offscreen-content-hidden", "weight": 0}, {"id": "custom-controls-labels", "weight": 0}, {"id": "custom-controls-roles", "weight": 0}, {"id": "empty-heading", "weight": 0, "group": "hidden"}, {"id": "identical-links-same-purpose", "weight": 0, "group": "hidden"}, {"id": "landmark-one-main", "weight": 0, "group": "hidden"}, {"id": "label-content-name-mismatch", "weight": 0, "group": "hidden"}, {"id": "table-fake-caption", "weight": 0, "group": "hidden"}, {"id": "td-has-header", "weight": 0, "group": "hidden"}], "id": "accessibility", "score": null}, "best-practices": {"title": "最佳做法", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "is-on-https", "weight": 5, "group": "best-practices-trust-safety"}, {"id": "redirects-http", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "geolocation-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "notification-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "csp-xss", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "has-hsts", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "origin-isolation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "clickjacking-mitigation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "paste-preventing-inputs", "weight": 3, "group": "best-practices-ux"}, {"id": "image-aspect-ratio", "weight": 1, "group": "best-practices-ux"}, {"id": "image-size-responsive", "weight": 1, "group": "best-practices-ux"}, {"id": "viewport", "weight": 1, "group": "best-practices-ux"}, {"id": "font-size", "weight": 1, "group": "best-practices-ux"}, {"id": "doctype", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "charset", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "js-libraries", "weight": 0, "group": "best-practices-general"}, {"id": "deprecations", "weight": 5, "group": "best-practices-general"}, {"id": "third-party-cookies", "weight": 5, "group": "best-practices-general"}, {"id": "errors-in-console", "weight": 1, "group": "best-practices-general"}, {"id": "valid-source-maps", "weight": 0, "group": "best-practices-general"}, {"id": "inspector-issues", "weight": 1, "group": "best-practices-general"}], "id": "best-practices", "score": null}, "seo": {"title": "SEO", "description": "此类检查可确保您的网页遵循了基本的搜索引擎优化建议。还有很多其他因素可能会影响您的网页在搜索引擎结果中的排名，但未被 Lighthouse 纳入此处的评估范围，其中包括[核心网页指标](https://web.dev/explore/vitals)衡量结果。[详细了解 Google Search Essentials](https://support.google.com/webmasters/answer/35769)。", "manualDescription": "请在您的网站上运行这些额外的验证程序，以检查其他 SEO 最佳做法。", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "is-crawlable", "weight": 4.043478260869565, "group": "seo-crawl"}, {"id": "document-title", "weight": 1, "group": "seo-content"}, {"id": "meta-description", "weight": 1, "group": "seo-content"}, {"id": "http-status-code", "weight": 1, "group": "seo-crawl"}, {"id": "link-text", "weight": 1, "group": "seo-content"}, {"id": "crawlable-anchors", "weight": 1, "group": "seo-crawl"}, {"id": "robots-txt", "weight": 1, "group": "seo-crawl"}, {"id": "image-alt", "weight": 1, "group": "seo-content"}, {"id": "hreflang", "weight": 1, "group": "seo-content"}, {"id": "canonical", "weight": 1, "group": "seo-content"}, {"id": "structured-data", "weight": 0}], "id": "seo", "score": null}}, "categoryGroups": {"metrics": {"title": "指标"}, "insights": {"title": "Insights", "description": "These insights are also available in the Chrome DevTools Performance Panel - [record a trace](https://developer.chrome.com/docs/devtools/performance/reference) to view more detailed information."}, "diagnostics": {"title": "诊断结果", "description": "详细了解您的应用的性能。这些数字不会[直接影响](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)性能得分。"}, "a11y-best-practices": {"title": "最佳做法", "description": "这些条目突出显示了常见的无障碍功能最佳做法。"}, "a11y-color-contrast": {"title": "对比度", "description": "这些提示旨在帮助改进您的内容的易读性。"}, "a11y-names-labels": {"title": "名称和标签", "description": "这些提示旨在帮助改进您的应用内控件的语义。这可以改善辅助技术（例如屏幕阅读器）用户的体验。"}, "a11y-navigation": {"title": "导航", "description": "这些提示旨在改进您应用中的键盘导航。"}, "a11y-aria": {"title": "ARIA", "description": "这些提示旨在帮助改进 ARIA 在您的应用内的使用情况，从而改善辅助技术（例如屏幕阅读器）用户的体验。"}, "a11y-language": {"title": "国际化和本地化", "description": "这些提示旨在让不同语言区域中的用户能够更好地解读您的内容。"}, "a11y-audio-video": {"title": "音频和视频", "description": "这提示旨在为音频和视频提供替代内容。这或许能改善听障用户或视障用户的体验。"}, "a11y-tables-lists": {"title": "表格和列表", "description": "这些提示旨在改善使用辅助技术（例如屏幕阅读器）查看表格数据或列表数据的体验。"}, "seo-mobile": {"title": "适合移动设备", "description": "请确保您的网页适合移动设备，以便用户无需缩放即可轻松阅读内容页面。[了解如何制作适合移动设备的网页](https://developers.google.com/search/mobile-sites/)。"}, "seo-content": {"title": "内容最佳做法", "description": "请确保您的 HTML 格式正确，以便抓取工具更好地了解您的应用的内容。"}, "seo-crawl": {"title": "抓取和编入索引", "description": "若想让您的应用显示在搜索结果中，您需要先授权抓取工具访问该应用。"}, "best-practices-trust-safety": {"title": "信任与安全"}, "best-practices-ux": {"title": "用户体验"}, "best-practices-browser-compat": {"title": "浏览器兼容性"}, "best-practices-general": {"title": "常规"}, "hidden": {"title": ""}}, "stackPacks": [], "timing": {"entries": [{"startTime": 734.2, "name": "lh:config", "duration": 439.2, "entryType": "measure"}, {"startTime": 736, "name": "lh:config:resolveArtifactsToDefns", "duration": 3.6, "entryType": "measure"}, {"startTime": 1173.6, "name": "lh:runner:gather", "duration": 36498.3, "entryType": "measure"}, {"startTime": 1173.9, "name": "lh:driver:connect", "duration": 291.5, "entryType": "measure"}, {"startTime": 1465.7, "name": "lh:driver:navigate", "duration": 2283.3, "entryType": "measure"}, {"startTime": 3749.2, "name": "lh:gather:getBenchmarkIndex", "duration": 1688.7, "entryType": "measure"}, {"startTime": 5438.1, "name": "lh:gather:getVersion", "duration": 10.5, "entryType": "measure"}, {"startTime": 5449, "name": "lh:prepare:navigationMode", "duration": 124.2, "entryType": "measure"}, {"startTime": 5512.2, "name": "lh:storage:clearDataForOrigin", "duration": 20.2, "entryType": "measure"}, {"startTime": 5532.6, "name": "lh:storage:clearBrowserCaches", "duration": 36.2, "entryType": "measure"}, {"startTime": 5570.3, "name": "lh:gather:prepareThrottlingAndNetwork", "duration": 2.9, "entryType": "measure"}, {"startTime": 37663.2, "name": "lh:computed:NetworkRecords", "duration": 3.8, "entryType": "measure"}, {"startTime": 37672.1, "name": "lh:runner:audit", "duration": 392.5, "entryType": "measure"}, {"startTime": 37672.2, "name": "lh:runner:auditing", "duration": 391.7, "entryType": "measure"}, {"startTime": 37675.3, "name": "lh:audit:is-on-https", "duration": 5.5, "entryType": "measure"}, {"startTime": 37681.5, "name": "lh:audit:redirects-http", "duration": 2.1, "entryType": "measure"}, {"startTime": 37684.5, "name": "lh:audit:viewport", "duration": 5.3, "entryType": "measure"}, {"startTime": 37690.6, "name": "lh:audit:first-contentful-paint", "duration": 1.7, "entryType": "measure"}, {"startTime": 37692.9, "name": "lh:audit:largest-contentful-paint", "duration": 1.7, "entryType": "measure"}, {"startTime": 37695, "name": "lh:audit:first-meaningful-paint", "duration": 1.8, "entryType": "measure"}, {"startTime": 37697.2, "name": "lh:audit:speed-index", "duration": 1.7, "entryType": "measure"}, {"startTime": 37698.9, "name": "lh:audit:screenshot-thumbnails", "duration": 0.3, "entryType": "measure"}, {"startTime": 37699.3, "name": "lh:audit:final-screenshot", "duration": 0.2, "entryType": "measure"}, {"startTime": 37700, "name": "lh:audit:total-blocking-time", "duration": 2.7, "entryType": "measure"}, {"startTime": 37703.3, "name": "lh:audit:max-potential-fid", "duration": 2.1, "entryType": "measure"}, {"startTime": 37705.7, "name": "lh:audit:cumulative-layout-shift", "duration": 1.7, "entryType": "measure"}, {"startTime": 37707.9, "name": "lh:audit:errors-in-console", "duration": 2.3, "entryType": "measure"}, {"startTime": 37710.8, "name": "lh:audit:server-response-time", "duration": 2.1, "entryType": "measure"}, {"startTime": 37713.4, "name": "lh:audit:interactive", "duration": 1.9, "entryType": "measure"}, {"startTime": 37715.7, "name": "lh:audit:user-timings", "duration": 1.7, "entryType": "measure"}, {"startTime": 37717.9, "name": "lh:audit:critical-request-chains", "duration": 3.5, "entryType": "measure"}, {"startTime": 37721.7, "name": "lh:audit:redirects", "duration": 1.5, "entryType": "measure"}, {"startTime": 37723.7, "name": "lh:audit:image-aspect-ratio", "duration": 2.6, "entryType": "measure"}, {"startTime": 37726.9, "name": "lh:audit:image-size-responsive", "duration": 1.6, "entryType": "measure"}, {"startTime": 37729, "name": "lh:audit:deprecations", "duration": 1.8, "entryType": "measure"}, {"startTime": 37731.3, "name": "lh:audit:third-party-cookies", "duration": 1.8, "entryType": "measure"}, {"startTime": 37733.7, "name": "lh:audit:mainthread-work-breakdown", "duration": 3.2, "entryType": "measure"}, {"startTime": 37737.9, "name": "lh:audit:bootup-time", "duration": 2.2, "entryType": "measure"}, {"startTime": 37740.5, "name": "lh:audit:uses-rel-preconnect", "duration": 1.7, "entryType": "measure"}, {"startTime": 37742.7, "name": "lh:audit:font-display", "duration": 1.8, "entryType": "measure"}, {"startTime": 37744.5, "name": "lh:audit:diagnostics", "duration": 0.1, "entryType": "measure"}, {"startTime": 37744.7, "name": "lh:audit:network-requests", "duration": 0, "entryType": "measure"}, {"startTime": 37745.2, "name": "lh:audit:network-rtt", "duration": 1.4, "entryType": "measure"}, {"startTime": 37747, "name": "lh:audit:network-server-latency", "duration": 1.5, "entryType": "measure"}, {"startTime": 37748.5, "name": "lh:audit:main-thread-tasks", "duration": 0.1, "entryType": "measure"}, {"startTime": 37748.6, "name": "lh:audit:metrics", "duration": 0.1, "entryType": "measure"}, {"startTime": 37748.7, "name": "lh:audit:resource-summary", "duration": 0.1, "entryType": "measure"}, {"startTime": 37749.5, "name": "lh:audit:third-party-summary", "duration": 2, "entryType": "measure"}, {"startTime": 37752.7, "name": "lh:audit:third-party-facades", "duration": 2.4, "entryType": "measure"}, {"startTime": 37755.7, "name": "lh:audit:largest-contentful-paint-element", "duration": 1.3, "entryType": "measure"}, {"startTime": 37757.5, "name": "lh:audit:lcp-lazy-loaded", "duration": 1.7, "entryType": "measure"}, {"startTime": 37759.7, "name": "lh:audit:layout-shifts", "duration": 1.7, "entryType": "measure"}, {"startTime": 37761.7, "name": "lh:audit:long-tasks", "duration": 1.3, "entryType": "measure"}, {"startTime": 37763.4, "name": "lh:audit:non-composited-animations", "duration": 1.3, "entryType": "measure"}, {"startTime": 37765.2, "name": "lh:audit:unsized-images", "duration": 1.9, "entryType": "measure"}, {"startTime": 37767.6, "name": "lh:audit:valid-source-maps", "duration": 2.5, "entryType": "measure"}, {"startTime": 37770.3, "name": "lh:audit:prioritize-lcp-image", "duration": 1.5, "entryType": "measure"}, {"startTime": 37772.3, "name": "lh:audit:csp-xss", "duration": 1.7, "entryType": "measure"}, {"startTime": 37774.4, "name": "lh:audit:has-hsts", "duration": 1.5, "entryType": "measure"}, {"startTime": 37776.3, "name": "lh:audit:origin-isolation", "duration": 1.3, "entryType": "measure"}, {"startTime": 37778, "name": "lh:audit:clickjacking-mitigation", "duration": 1.6, "entryType": "measure"}, {"startTime": 37779.6, "name": "lh:audit:script-treemap-data", "duration": 0.3, "entryType": "measure"}, {"startTime": 37780.5, "name": "lh:audit:accesskeys", "duration": 1.7, "entryType": "measure"}, {"startTime": 37782.6, "name": "lh:audit:aria-allowed-attr", "duration": 1.9, "entryType": "measure"}, {"startTime": 37785, "name": "lh:audit:aria-allowed-role", "duration": 2.1, "entryType": "measure"}, {"startTime": 37787.7, "name": "lh:audit:aria-command-name", "duration": 2.1, "entryType": "measure"}, {"startTime": 37790.3, "name": "lh:audit:aria-conditional-attr", "duration": 22.9, "entryType": "measure"}, {"startTime": 37814, "name": "lh:audit:aria-deprecated-role", "duration": 1.6, "entryType": "measure"}, {"startTime": 37815.9, "name": "lh:audit:aria-dialog-name", "duration": 1.3, "entryType": "measure"}, {"startTime": 37817.6, "name": "lh:audit:aria-hidden-body", "duration": 1.5, "entryType": "measure"}, {"startTime": 37819.6, "name": "lh:audit:aria-hidden-focus", "duration": 2.8, "entryType": "measure"}, {"startTime": 37822.8, "name": "lh:audit:aria-input-field-name", "duration": 1.3, "entryType": "measure"}, {"startTime": 37824.5, "name": "lh:audit:aria-meter-name", "duration": 1.3, "entryType": "measure"}, {"startTime": 37826.2, "name": "lh:audit:aria-progressbar-name", "duration": 1.3, "entryType": "measure"}, {"startTime": 37827.8, "name": "lh:audit:aria-prohibited-attr", "duration": 1.4, "entryType": "measure"}, {"startTime": 37829.6, "name": "lh:audit:aria-required-attr", "duration": 1.3, "entryType": "measure"}, {"startTime": 37831.2, "name": "lh:audit:aria-required-children", "duration": 4.2, "entryType": "measure"}, {"startTime": 37836.1, "name": "lh:audit:aria-required-parent", "duration": 2.3, "entryType": "measure"}, {"startTime": 37838.7, "name": "lh:audit:aria-roles", "duration": 1.2, "entryType": "measure"}, {"startTime": 37840.3, "name": "lh:audit:aria-text", "duration": 1.4, "entryType": "measure"}, {"startTime": 37842, "name": "lh:audit:aria-toggle-field-name", "duration": 1.4, "entryType": "measure"}, {"startTime": 37843.8, "name": "lh:audit:aria-tooltip-name", "duration": 4.2, "entryType": "measure"}, {"startTime": 37848.4, "name": "lh:audit:aria-treeitem-name", "duration": 1.2, "entryType": "measure"}, {"startTime": 37850, "name": "lh:audit:aria-valid-attr-value", "duration": 1.1, "entryType": "measure"}, {"startTime": 37851.4, "name": "lh:audit:aria-valid-attr", "duration": 1.4, "entryType": "measure"}, {"startTime": 37853.2, "name": "lh:audit:button-name", "duration": 1.3, "entryType": "measure"}, {"startTime": 37854.8, "name": "lh:audit:bypass", "duration": 1.1, "entryType": "measure"}, {"startTime": 37856.3, "name": "lh:audit:color-contrast", "duration": 1.3, "entryType": "measure"}, {"startTime": 37857.9, "name": "lh:audit:definition-list", "duration": 1.4, "entryType": "measure"}, {"startTime": 37859.7, "name": "lh:audit:dlitem", "duration": 2, "entryType": "measure"}, {"startTime": 37862.2, "name": "lh:audit:document-title", "duration": 1.3, "entryType": "measure"}, {"startTime": 37863.7, "name": "lh:audit:duplicate-id-aria", "duration": 1.2, "entryType": "measure"}, {"startTime": 37865.2, "name": "lh:audit:empty-heading", "duration": 1.3, "entryType": "measure"}, {"startTime": 37866.8, "name": "lh:audit:form-field-multiple-labels", "duration": 1.2, "entryType": "measure"}, {"startTime": 37868.4, "name": "lh:audit:frame-title", "duration": 1.3, "entryType": "measure"}, {"startTime": 37870.1, "name": "lh:audit:heading-order", "duration": 5.5, "entryType": "measure"}, {"startTime": 37876, "name": "lh:audit:html-has-lang", "duration": 1.6, "entryType": "measure"}, {"startTime": 37878, "name": "lh:audit:html-lang-valid", "duration": 1.3, "entryType": "measure"}, {"startTime": 37879.8, "name": "lh:audit:html-xml-lang-mismatch", "duration": 2, "entryType": "measure"}, {"startTime": 37882.1, "name": "lh:audit:identical-links-same-purpose", "duration": 1.3, "entryType": "measure"}, {"startTime": 37883.8, "name": "lh:audit:image-alt", "duration": 1.2, "entryType": "measure"}, {"startTime": 37885.4, "name": "lh:audit:image-redundant-alt", "duration": 1.5, "entryType": "measure"}, {"startTime": 37887.3, "name": "lh:audit:input-button-name", "duration": 1.2, "entryType": "measure"}, {"startTime": 37888.9, "name": "lh:audit:input-image-alt", "duration": 1.2, "entryType": "measure"}, {"startTime": 37890.4, "name": "lh:audit:label-content-name-mismatch", "duration": 8.8, "entryType": "measure"}, {"startTime": 37899.9, "name": "lh:audit:label", "duration": 1.6, "entryType": "measure"}, {"startTime": 37901.8, "name": "lh:audit:landmark-one-main", "duration": 1.9, "entryType": "measure"}, {"startTime": 37904.2, "name": "lh:audit:link-name", "duration": 1.7, "entryType": "measure"}, {"startTime": 37906.3, "name": "lh:audit:link-in-text-block", "duration": 1.8, "entryType": "measure"}, {"startTime": 37908.5, "name": "lh:audit:list", "duration": 1.8, "entryType": "measure"}, {"startTime": 37910.8, "name": "lh:audit:listitem", "duration": 1.7, "entryType": "measure"}, {"startTime": 37913.1, "name": "lh:audit:meta-refresh", "duration": 1.7, "entryType": "measure"}, {"startTime": 37915.4, "name": "lh:audit:meta-viewport", "duration": 1.9, "entryType": "measure"}, {"startTime": 37917.7, "name": "lh:audit:object-alt", "duration": 1.9, "entryType": "measure"}, {"startTime": 37920.1, "name": "lh:audit:select-name", "duration": 1.5, "entryType": "measure"}, {"startTime": 37922, "name": "lh:audit:skip-link", "duration": 2.3, "entryType": "measure"}, {"startTime": 37924.7, "name": "lh:audit:tabindex", "duration": 1.8, "entryType": "measure"}, {"startTime": 37927.1, "name": "lh:audit:table-duplicate-name", "duration": 1.8, "entryType": "measure"}, {"startTime": 37929.5, "name": "lh:audit:table-fake-caption", "duration": 2, "entryType": "measure"}, {"startTime": 37931.9, "name": "lh:audit:target-size", "duration": 1.4, "entryType": "measure"}, {"startTime": 37933.8, "name": "lh:audit:td-has-header", "duration": 1.7, "entryType": "measure"}, {"startTime": 37936.1, "name": "lh:audit:td-headers-attr", "duration": 1.9, "entryType": "measure"}, {"startTime": 37938.6, "name": "lh:audit:th-has-data-cells", "duration": 2.1, "entryType": "measure"}, {"startTime": 37941.2, "name": "lh:audit:valid-lang", "duration": 2, "entryType": "measure"}, {"startTime": 37943.6, "name": "lh:audit:video-caption", "duration": 1.8, "entryType": "measure"}, {"startTime": 37945.4, "name": "lh:audit:custom-controls-labels", "duration": 0.2, "entryType": "measure"}, {"startTime": 37945.6, "name": "lh:audit:custom-controls-roles", "duration": 0.2, "entryType": "measure"}, {"startTime": 37945.8, "name": "lh:audit:focus-traps", "duration": 0.2, "entryType": "measure"}, {"startTime": 37946, "name": "lh:audit:focusable-controls", "duration": 0, "entryType": "measure"}, {"startTime": 37946, "name": "lh:audit:interactive-element-affordance", "duration": 0.1, "entryType": "measure"}, {"startTime": 37946.1, "name": "lh:audit:logical-tab-order", "duration": 0.2, "entryType": "measure"}, {"startTime": 37946.3, "name": "lh:audit:managed-focus", "duration": 0.1, "entryType": "measure"}, {"startTime": 37946.4, "name": "lh:audit:offscreen-content-hidden", "duration": 0, "entryType": "measure"}, {"startTime": 37946.4, "name": "lh:audit:use-landmarks", "duration": 0.2, "entryType": "measure"}, {"startTime": 37946.6, "name": "lh:audit:visual-order-follows-dom", "duration": 0.1, "entryType": "measure"}, {"startTime": 37947.2, "name": "lh:audit:uses-long-cache-ttl", "duration": 1.8, "entryType": "measure"}, {"startTime": 37949.4, "name": "lh:audit:total-byte-weight", "duration": 1.6, "entryType": "measure"}, {"startTime": 37951.4, "name": "lh:audit:offscreen-images", "duration": 1.4, "entryType": "measure"}, {"startTime": 37953.5, "name": "lh:audit:render-blocking-resources", "duration": 1.9, "entryType": "measure"}, {"startTime": 37955.6, "name": "lh:audit:unminified-css", "duration": 1.3, "entryType": "measure"}, {"startTime": 37957.3, "name": "lh:audit:unminified-javascript", "duration": 1.1, "entryType": "measure"}, {"startTime": 37958.8, "name": "lh:audit:unused-css-rules", "duration": 1.4, "entryType": "measure"}, {"startTime": 37960.6, "name": "lh:audit:unused-javascript", "duration": 1.4, "entryType": "measure"}, {"startTime": 37962.3, "name": "lh:audit:modern-image-formats", "duration": 1.3, "entryType": "measure"}, {"startTime": 37964.1, "name": "lh:audit:uses-optimized-images", "duration": 1.5, "entryType": "measure"}, {"startTime": 37965.9, "name": "lh:audit:uses-text-compression", "duration": 1.3, "entryType": "measure"}, {"startTime": 37967.6, "name": "lh:audit:uses-responsive-images", "duration": 1.5, "entryType": "measure"}, {"startTime": 37969.6, "name": "lh:audit:efficient-animated-content", "duration": 1.6, "entryType": "measure"}, {"startTime": 37971.4, "name": "lh:audit:duplicated-javascript", "duration": 1.2, "entryType": "measure"}, {"startTime": 37973.1, "name": "lh:audit:legacy-javascript", "duration": 1.8, "entryType": "measure"}, {"startTime": 37975.3, "name": "lh:audit:doctype", "duration": 1.7, "entryType": "measure"}, {"startTime": 37977.5, "name": "lh:audit:charset", "duration": 1.9, "entryType": "measure"}, {"startTime": 37980, "name": "lh:audit:dom-size", "duration": 1.9, "entryType": "measure"}, {"startTime": 37982.4, "name": "lh:audit:geolocation-on-start", "duration": 2.4, "entryType": "measure"}, {"startTime": 37985.7, "name": "lh:audit:inspector-issues", "duration": 2.4, "entryType": "measure"}, {"startTime": 37988.6, "name": "lh:audit:no-document-write", "duration": 1.8, "entryType": "measure"}, {"startTime": 37990.9, "name": "lh:audit:js-libraries", "duration": 1.3, "entryType": "measure"}, {"startTime": 37992.7, "name": "lh:audit:notification-on-start", "duration": 1.9, "entryType": "measure"}, {"startTime": 37995, "name": "lh:audit:paste-preventing-inputs", "duration": 1.7, "entryType": "measure"}, {"startTime": 37997.1, "name": "lh:audit:uses-http2", "duration": 1.2, "entryType": "measure"}, {"startTime": 37998.9, "name": "lh:audit:uses-passive-event-listeners", "duration": 1.9, "entryType": "measure"}, {"startTime": 38001.3, "name": "lh:audit:meta-description", "duration": 1.8, "entryType": "measure"}, {"startTime": 38003.5, "name": "lh:audit:http-status-code", "duration": 1.7, "entryType": "measure"}, {"startTime": 38005.8, "name": "lh:audit:font-size", "duration": 1.7, "entryType": "measure"}, {"startTime": 38008, "name": "lh:audit:link-text", "duration": 1.5, "entryType": "measure"}, {"startTime": 38010, "name": "lh:audit:crawlable-anchors", "duration": 1.7, "entryType": "measure"}, {"startTime": 38012.2, "name": "lh:audit:is-crawlable", "duration": 1.7, "entryType": "measure"}, {"startTime": 38014.5, "name": "lh:audit:robots-txt", "duration": 1.5, "entryType": "measure"}, {"startTime": 38016.5, "name": "lh:audit:hreflang", "duration": 1.6, "entryType": "measure"}, {"startTime": 38018.8, "name": "lh:audit:canonical", "duration": 1.7, "entryType": "measure"}, {"startTime": 38020.9, "name": "lh:audit:structured-data", "duration": 1.4, "entryType": "measure"}, {"startTime": 38022.8, "name": "lh:audit:bf-cache", "duration": 1.8, "entryType": "measure"}, {"startTime": 38025, "name": "lh:audit:cache-insight", "duration": 1.5, "entryType": "measure"}, {"startTime": 38027, "name": "lh:audit:cls-culprits-insight", "duration": 1.7, "entryType": "measure"}, {"startTime": 38029.1, "name": "lh:audit:document-latency-insight", "duration": 2.3, "entryType": "measure"}, {"startTime": 38031.8, "name": "lh:audit:dom-size-insight", "duration": 1.8, "entryType": "measure"}, {"startTime": 38034, "name": "lh:audit:duplicated-javascript-insight", "duration": 1.6, "entryType": "measure"}, {"startTime": 38036.2, "name": "lh:audit:font-display-insight", "duration": 1.9, "entryType": "measure"}, {"startTime": 38038.5, "name": "lh:audit:forced-reflow-insight", "duration": 1.9, "entryType": "measure"}, {"startTime": 38040.8, "name": "lh:audit:image-delivery-insight", "duration": 1.7, "entryType": "measure"}, {"startTime": 38043, "name": "lh:audit:interaction-to-next-paint-insight", "duration": 1.7, "entryType": "measure"}, {"startTime": 38045.2, "name": "lh:audit:lcp-discovery-insight", "duration": 1.7, "entryType": "measure"}, {"startTime": 38047.3, "name": "lh:audit:lcp-phases-insight", "duration": 1.7, "entryType": "measure"}, {"startTime": 38049.7, "name": "lh:audit:legacy-javascript-insight", "duration": 2, "entryType": "measure"}, {"startTime": 38052.3, "name": "lh:audit:modern-http-insight", "duration": 1.9, "entryType": "measure"}, {"startTime": 38054.8, "name": "lh:audit:network-dependency-tree-insight", "duration": 1.9, "entryType": "measure"}, {"startTime": 38057.3, "name": "lh:audit:render-blocking-insight", "duration": 1.9, "entryType": "measure"}, {"startTime": 38059.7, "name": "lh:audit:third-parties-insight", "duration": 2, "entryType": "measure"}, {"startTime": 38062.2, "name": "lh:audit:viewport-insight", "duration": 1.7, "entryType": "measure"}, {"startTime": 38063.9, "name": "lh:runner:generate", "duration": 0.7, "entryType": "measure"}], "total": 36890.8}, "i18n": {"rendererFormattedStrings": {"calculatorLink": "查看计算器。", "collapseView": "收起视图", "crcInitialNavigation": "初始导航", "crcLongestDurationLabel": "关键路径延迟时间上限：", "dropdownCopyJSON": "复制 JSON", "dropdownDarkTheme": "开启/关闭深色主题", "dropdownPrintExpanded": "展开打印对话框", "dropdownPrintSummary": "打印摘要", "dropdownSaveGist": "另存为 Gist", "dropdownSaveHTML": "另存为 HTML", "dropdownSaveJSON": "另存为 JSON", "dropdownViewUnthrottledTrace": "查看原始跟踪记录", "dropdownViewer": "在查看器中打开", "errorLabel": "出错了！", "errorMissingAuditInfo": "报告错误：没有任何审核信息", "expandView": "展开视图", "firstPartyChipLabel": "第一方", "footerIssue": "提交问题", "hide": "隐藏", "labDataTitle": "实验室数据", "lsPerformanceCategoryDescription": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) 使用模拟的移动网络对当前页面进行的分析。这些值都是估算值，且可能会因时而异。", "manualAuditsGroupTitle": "待手动检查的其他项", "notApplicableAuditsGroupTitle": "不适用", "openInANewTabTooltip": "在新标签页中打开", "opportunityResourceColumnLabel": "优化建议", "opportunitySavingsColumnLabel": "有望节省的总时间（估算值）", "passedAuditsGroupTitle": "已通过的审核", "runtimeAnalysisWindow": "初始网页加载", "runtimeAnalysisWindowSnapshot": "时间点快照", "runtimeAnalysisWindowTimespan": "用户互动时间跨度", "runtimeCustom": "自定义节流", "runtimeDesktopEmulation": "模拟桌面设备", "runtimeMobileEmulation": "模拟了 Moto G 电源", "runtimeNoEmulation": "无模拟", "runtimeSettingsAxeVersion": "Axe 版本", "runtimeSettingsBenchmark": "未节流限制的 CPU/内存功耗", "runtimeSettingsCPUThrottling": "CPU 节流", "runtimeSettingsDevice": "设备", "runtimeSettingsNetworkThrottling": "网络节流", "runtimeSettingsScreenEmulation": "屏幕模拟", "runtimeSettingsUANetwork": "用户代理（网络）", "runtimeSingleLoad": "单页会话", "runtimeSingleLoadTooltip": "此数据是取自单页会话，现场数据则是汇总了许多次会话。", "runtimeSlow4g": "低速 4G 节流", "runtimeUnknown": "不明", "show": "显示", "showRelevantAudits": "显示与所选指标相关的评估结果：", "snippetCollapseButtonLabel": "收起代码段", "snippetExpandButtonLabel": "展开代码段", "thirdPartyResourcesLabel": "显示第三方资源", "throttlingProvided": "由环境提供", "toplevelWarningsMessage": "此次 Lighthouse 运行并不顺利，原因如下：", "unattributable": "无法归因", "varianceDisclaimer": "这些都是估算值，且可能会因时而异。系统会直接基于这些指标来[计算性能得分](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)。", "viewTraceLabel": "查看跟踪记录", "viewTreemapLabel": "查看树状图", "warningAuditsGroupTitle": "已顺利通过审核，但有警告消息", "warningHeader": "警告： "}, "icuMessagePaths": {"core/lib/lh-error.js | noFcp": [{"values": {"errorCode": "NO_FCP"}, "path": "runtimeError.message"}, {"values": {"errorCode": "NO_FCP"}, "path": "runWarnings[0]"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[is-on-https].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[redirects-http].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.viewport.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[first-contentful-paint].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[largest-contentful-paint].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[first-meaningful-paint].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[speed-index].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[screenshot-thumbnails].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[final-screenshot].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[total-blocking-time].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[max-potential-fid].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[cumulative-layout-shift].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[errors-in-console].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[server-response-time].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.interactive.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[user-timings].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[critical-request-chains].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.redirects.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[image-aspect-ratio].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[image-size-responsive].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.deprecations.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[third-party-cookies].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[mainthread-work-breakdown].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[bootup-time].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[uses-rel-preconnect].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[font-display].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.diagnostics.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[network-requests].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[network-rtt].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[network-server-latency].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[main-thread-tasks].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.metrics.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[resource-summary].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[third-party-summary].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[third-party-facades].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[largest-contentful-paint-element].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[lcp-lazy-loaded].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[layout-shifts].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[long-tasks].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[non-composited-animations].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[unsized-images].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[valid-source-maps].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[prioritize-lcp-image].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[csp-xss].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[has-hsts].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[origin-isolation].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[clickjacking-mitigation].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[script-treemap-data].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.accesskeys.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-allowed-attr].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-allowed-role].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-command-name].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-conditional-attr].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-deprecated-role].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-dialog-name].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-hidden-body].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-hidden-focus].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-input-field-name].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-meter-name].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-progressbar-name].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-prohibited-attr].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-required-attr].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-required-children].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-required-parent].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-roles].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-text].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-toggle-field-name].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-tooltip-name].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-treeitem-name].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-valid-attr-value].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[aria-valid-attr].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[button-name].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.bypass.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[color-contrast].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[definition-list].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.dlitem.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[document-title].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[duplicate-id-aria].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[empty-heading].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[form-field-multiple-labels].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[frame-title].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[heading-order].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[html-has-lang].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[html-lang-valid].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[html-xml-lang-mismatch].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[identical-links-same-purpose].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[image-alt].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[image-redundant-alt].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[input-button-name].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[input-image-alt].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[label-content-name-mismatch].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.label.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[landmark-one-main].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[link-name].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[link-in-text-block].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.list.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.listitem.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[meta-refresh].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[meta-viewport].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[object-alt].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[select-name].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[skip-link].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.tabindex.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[table-duplicate-name].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[table-fake-caption].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[target-size].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[td-has-header].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[td-headers-attr].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[th-has-data-cells].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[valid-lang].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[video-caption].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[custom-controls-labels].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[custom-controls-roles].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[focus-traps].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[focusable-controls].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[interactive-element-affordance].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[logical-tab-order].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[managed-focus].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[offscreen-content-hidden].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[use-landmarks].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[visual-order-follows-dom].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[uses-long-cache-ttl].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[total-byte-weight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[offscreen-images].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[render-blocking-resources].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[unminified-css].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[unminified-javascript].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[unused-css-rules].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[unused-javascript].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[modern-image-formats].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[uses-optimized-images].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[uses-text-compression].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[uses-responsive-images].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[efficient-animated-content].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[duplicated-javascript].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[legacy-javascript].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.doctype.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.charset.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[dom-size].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[geolocation-on-start].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[inspector-issues].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[no-document-write].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[js-libraries].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[notification-on-start].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[paste-preventing-inputs].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[uses-http2].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[uses-passive-event-listeners].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[meta-description].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[http-status-code].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[font-size].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[link-text].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[crawlable-anchors].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[is-crawlable].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[robots-txt].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.hreflang.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits.canonical.errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[structured-data].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[bf-cache].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[cache-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[cls-culprits-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[document-latency-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[dom-size-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[duplicated-javascript-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[font-display-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[forced-reflow-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[image-delivery-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[interaction-to-next-paint-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[lcp-discovery-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[lcp-phases-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[legacy-javascript-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[modern-http-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[network-dependency-tree-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[render-blocking-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[third-parties-insight].errorMessage"}, {"values": {"errorCode": "NO_FCP"}, "path": "audits[viewport-insight].errorMessage"}], "core/audits/is-on-https.js | title": ["audits[is-on-https].title"], "core/audits/is-on-https.js | description": ["audits[is-on-https].description"], "core/audits/redirects-http.js | title": ["audits[redirects-http].title"], "core/audits/redirects-http.js | description": ["audits[redirects-http].description"], "core/audits/viewport.js | title": ["audits.viewport.title"], "core/audits/viewport.js | description": ["audits.viewport.description"], "core/lib/i18n/i18n.js | firstContentfulPaintMetric": ["audits[first-contentful-paint].title"], "core/audits/metrics/first-contentful-paint.js | description": ["audits[first-contentful-paint].description"], "core/lib/i18n/i18n.js | largestContentfulPaintMetric": ["audits[largest-contentful-paint].title"], "core/audits/metrics/largest-contentful-paint.js | description": ["audits[largest-contentful-paint].description"], "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": ["audits[first-meaningful-paint].title"], "core/audits/metrics/first-meaningful-paint.js | description": ["audits[first-meaningful-paint].description"], "core/lib/i18n/i18n.js | speedIndexMetric": ["audits[speed-index].title"], "core/audits/metrics/speed-index.js | description": ["audits[speed-index].description"], "core/lib/i18n/i18n.js | totalBlockingTimeMetric": ["audits[total-blocking-time].title"], "core/audits/metrics/total-blocking-time.js | description": ["audits[total-blocking-time].description"], "core/lib/i18n/i18n.js | maxPotentialFIDMetric": ["audits[max-potential-fid].title"], "core/audits/metrics/max-potential-fid.js | description": ["audits[max-potential-fid].description"], "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": ["audits[cumulative-layout-shift].title"], "core/audits/metrics/cumulative-layout-shift.js | description": ["audits[cumulative-layout-shift].description"], "core/audits/errors-in-console.js | title": ["audits[errors-in-console].title"], "core/audits/errors-in-console.js | description": ["audits[errors-in-console].description"], "core/audits/server-response-time.js | title": ["audits[server-response-time].title"], "core/audits/server-response-time.js | description": ["audits[server-response-time].description"], "core/lib/i18n/i18n.js | interactiveMetric": ["audits.interactive.title"], "core/audits/metrics/interactive.js | description": ["audits.interactive.description"], "core/audits/user-timings.js | title": ["audits[user-timings].title"], "core/audits/user-timings.js | description": ["audits[user-timings].description"], "core/audits/critical-request-chains.js | title": ["audits[critical-request-chains].title"], "core/audits/critical-request-chains.js | description": ["audits[critical-request-chains].description"], "core/audits/redirects.js | title": ["audits.redirects.title"], "core/audits/redirects.js | description": ["audits.redirects.description"], "core/audits/image-aspect-ratio.js | title": ["audits[image-aspect-ratio].title"], "core/audits/image-aspect-ratio.js | description": ["audits[image-aspect-ratio].description"], "core/audits/image-size-responsive.js | title": ["audits[image-size-responsive].title"], "core/audits/image-size-responsive.js | description": ["audits[image-size-responsive].description"], "core/audits/deprecations.js | title": ["audits.deprecations.title"], "core/audits/deprecations.js | description": ["audits.deprecations.description"], "core/audits/third-party-cookies.js | title": ["audits[third-party-cookies].title"], "core/audits/third-party-cookies.js | description": ["audits[third-party-cookies].description"], "core/audits/mainthread-work-breakdown.js | title": ["audits[mainthread-work-breakdown].title"], "core/audits/mainthread-work-breakdown.js | description": ["audits[mainthread-work-breakdown].description"], "core/audits/bootup-time.js | title": ["audits[bootup-time].title"], "core/audits/bootup-time.js | description": ["audits[bootup-time].description"], "core/audits/uses-rel-preconnect.js | title": ["audits[uses-rel-preconnect].title"], "core/audits/uses-rel-preconnect.js | description": ["audits[uses-rel-preconnect].description"], "core/audits/font-display.js | title": ["audits[font-display].title"], "core/audits/font-display.js | description": ["audits[font-display].description"], "core/audits/network-rtt.js | title": ["audits[network-rtt].title"], "core/audits/network-rtt.js | description": ["audits[network-rtt].description"], "core/audits/network-server-latency.js | title": ["audits[network-server-latency].title"], "core/audits/network-server-latency.js | description": ["audits[network-server-latency].description"], "core/audits/third-party-summary.js | title": ["audits[third-party-summary].title"], "core/audits/third-party-summary.js | description": ["audits[third-party-summary].description"], "core/audits/third-party-facades.js | title": ["audits[third-party-facades].title"], "core/audits/third-party-facades.js | description": ["audits[third-party-facades].description"], "core/audits/largest-contentful-paint-element.js | title": ["audits[largest-contentful-paint-element].title"], "core/audits/largest-contentful-paint-element.js | description": ["audits[largest-contentful-paint-element].description"], "core/audits/lcp-lazy-loaded.js | title": ["audits[lcp-lazy-loaded].title"], "core/audits/lcp-lazy-loaded.js | description": ["audits[lcp-lazy-loaded].description"], "core/audits/layout-shifts.js | title": ["audits[layout-shifts].title"], "core/audits/layout-shifts.js | description": ["audits[layout-shifts].description"], "core/audits/long-tasks.js | title": ["audits[long-tasks].title"], "core/audits/long-tasks.js | description": ["audits[long-tasks].description"], "core/audits/non-composited-animations.js | title": ["audits[non-composited-animations].title"], "core/audits/non-composited-animations.js | description": ["audits[non-composited-animations].description"], "core/audits/unsized-images.js | title": ["audits[unsized-images].title"], "core/audits/unsized-images.js | description": ["audits[unsized-images].description"], "core/audits/valid-source-maps.js | title": ["audits[valid-source-maps].title"], "core/audits/valid-source-maps.js | description": ["audits[valid-source-maps].description"], "core/audits/prioritize-lcp-image.js | title": ["audits[prioritize-lcp-image].title"], "core/audits/prioritize-lcp-image.js | description": ["audits[prioritize-lcp-image].description"], "core/audits/csp-xss.js | title": ["audits[csp-xss].title"], "core/audits/csp-xss.js | description": ["audits[csp-xss].description"], "core/audits/has-hsts.js | title": ["audits[has-hsts].title"], "core/audits/has-hsts.js | description": ["audits[has-hsts].description"], "core/audits/origin-isolation.js | title": ["audits[origin-isolation].title"], "core/audits/origin-isolation.js | description": ["audits[origin-isolation].description"], "core/audits/clickjacking-mitigation.js | title": ["audits[clickjacking-mitigation].title"], "core/audits/clickjacking-mitigation.js | description": ["audits[clickjacking-mitigation].description"], "core/audits/accessibility/accesskeys.js | title": ["audits.accesskeys.title"], "core/audits/accessibility/accesskeys.js | description": ["audits.accesskeys.description"], "core/audits/accessibility/aria-allowed-attr.js | title": ["audits[aria-allowed-attr].title"], "core/audits/accessibility/aria-allowed-attr.js | description": ["audits[aria-allowed-attr].description"], "core/audits/accessibility/aria-allowed-role.js | title": ["audits[aria-allowed-role].title"], "core/audits/accessibility/aria-allowed-role.js | description": ["audits[aria-allowed-role].description"], "core/audits/accessibility/aria-command-name.js | title": ["audits[aria-command-name].title"], "core/audits/accessibility/aria-command-name.js | description": ["audits[aria-command-name].description"], "core/audits/accessibility/aria-conditional-attr.js | title": ["audits[aria-conditional-attr].title"], "core/audits/accessibility/aria-conditional-attr.js | description": ["audits[aria-conditional-attr].description"], "core/audits/accessibility/aria-deprecated-role.js | title": ["audits[aria-deprecated-role].title"], "core/audits/accessibility/aria-deprecated-role.js | description": ["audits[aria-deprecated-role].description"], "core/audits/accessibility/aria-dialog-name.js | title": ["audits[aria-dialog-name].title"], "core/audits/accessibility/aria-dialog-name.js | description": ["audits[aria-dialog-name].description"], "core/audits/accessibility/aria-hidden-body.js | title": ["audits[aria-hidden-body].title"], "core/audits/accessibility/aria-hidden-body.js | description": ["audits[aria-hidden-body].description"], "core/audits/accessibility/aria-hidden-focus.js | title": ["audits[aria-hidden-focus].title"], "core/audits/accessibility/aria-hidden-focus.js | description": ["audits[aria-hidden-focus].description"], "core/audits/accessibility/aria-input-field-name.js | title": ["audits[aria-input-field-name].title"], "core/audits/accessibility/aria-input-field-name.js | description": ["audits[aria-input-field-name].description"], "core/audits/accessibility/aria-meter-name.js | title": ["audits[aria-meter-name].title"], "core/audits/accessibility/aria-meter-name.js | description": ["audits[aria-meter-name].description"], "core/audits/accessibility/aria-progressbar-name.js | title": ["audits[aria-progressbar-name].title"], "core/audits/accessibility/aria-progressbar-name.js | description": ["audits[aria-progressbar-name].description"], "core/audits/accessibility/aria-prohibited-attr.js | title": ["audits[aria-prohibited-attr].title"], "core/audits/accessibility/aria-prohibited-attr.js | description": ["audits[aria-prohibited-attr].description"], "core/audits/accessibility/aria-required-attr.js | title": ["audits[aria-required-attr].title"], "core/audits/accessibility/aria-required-attr.js | description": ["audits[aria-required-attr].description"], "core/audits/accessibility/aria-required-children.js | title": ["audits[aria-required-children].title"], "core/audits/accessibility/aria-required-children.js | description": ["audits[aria-required-children].description"], "core/audits/accessibility/aria-required-parent.js | title": ["audits[aria-required-parent].title"], "core/audits/accessibility/aria-required-parent.js | description": ["audits[aria-required-parent].description"], "core/audits/accessibility/aria-roles.js | title": ["audits[aria-roles].title"], "core/audits/accessibility/aria-roles.js | description": ["audits[aria-roles].description"], "core/audits/accessibility/aria-text.js | title": ["audits[aria-text].title"], "core/audits/accessibility/aria-text.js | description": ["audits[aria-text].description"], "core/audits/accessibility/aria-toggle-field-name.js | title": ["audits[aria-toggle-field-name].title"], "core/audits/accessibility/aria-toggle-field-name.js | description": ["audits[aria-toggle-field-name].description"], "core/audits/accessibility/aria-tooltip-name.js | title": ["audits[aria-tooltip-name].title"], "core/audits/accessibility/aria-tooltip-name.js | description": ["audits[aria-tooltip-name].description"], "core/audits/accessibility/aria-treeitem-name.js | title": ["audits[aria-treeitem-name].title"], "core/audits/accessibility/aria-treeitem-name.js | description": ["audits[aria-treeitem-name].description"], "core/audits/accessibility/aria-valid-attr-value.js | title": ["audits[aria-valid-attr-value].title"], "core/audits/accessibility/aria-valid-attr-value.js | description": ["audits[aria-valid-attr-value].description"], "core/audits/accessibility/aria-valid-attr.js | title": ["audits[aria-valid-attr].title"], "core/audits/accessibility/aria-valid-attr.js | description": ["audits[aria-valid-attr].description"], "core/audits/accessibility/button-name.js | title": ["audits[button-name].title"], "core/audits/accessibility/button-name.js | description": ["audits[button-name].description"], "core/audits/accessibility/bypass.js | title": ["audits.bypass.title"], "core/audits/accessibility/bypass.js | description": ["audits.bypass.description"], "core/audits/accessibility/color-contrast.js | title": ["audits[color-contrast].title"], "core/audits/accessibility/color-contrast.js | description": ["audits[color-contrast].description"], "core/audits/accessibility/definition-list.js | title": ["audits[definition-list].title"], "core/audits/accessibility/definition-list.js | description": ["audits[definition-list].description"], "core/audits/accessibility/dlitem.js | title": ["audits.dlitem.title"], "core/audits/accessibility/dlitem.js | description": ["audits.dlitem.description"], "core/audits/accessibility/document-title.js | title": ["audits[document-title].title"], "core/audits/accessibility/document-title.js | description": ["audits[document-title].description"], "core/audits/accessibility/duplicate-id-aria.js | title": ["audits[duplicate-id-aria].title"], "core/audits/accessibility/duplicate-id-aria.js | description": ["audits[duplicate-id-aria].description"], "core/audits/accessibility/empty-heading.js | title": ["audits[empty-heading].title"], "core/audits/accessibility/empty-heading.js | description": ["audits[empty-heading].description"], "core/audits/accessibility/form-field-multiple-labels.js | title": ["audits[form-field-multiple-labels].title"], "core/audits/accessibility/form-field-multiple-labels.js | description": ["audits[form-field-multiple-labels].description"], "core/audits/accessibility/frame-title.js | title": ["audits[frame-title].title"], "core/audits/accessibility/frame-title.js | description": ["audits[frame-title].description"], "core/audits/accessibility/heading-order.js | title": ["audits[heading-order].title"], "core/audits/accessibility/heading-order.js | description": ["audits[heading-order].description"], "core/audits/accessibility/html-has-lang.js | title": ["audits[html-has-lang].title"], "core/audits/accessibility/html-has-lang.js | description": ["audits[html-has-lang].description"], "core/audits/accessibility/html-lang-valid.js | title": ["audits[html-lang-valid].title"], "core/audits/accessibility/html-lang-valid.js | description": ["audits[html-lang-valid].description"], "core/audits/accessibility/html-xml-lang-mismatch.js | title": ["audits[html-xml-lang-mismatch].title"], "core/audits/accessibility/html-xml-lang-mismatch.js | description": ["audits[html-xml-lang-mismatch].description"], "core/audits/accessibility/identical-links-same-purpose.js | title": ["audits[identical-links-same-purpose].title"], "core/audits/accessibility/identical-links-same-purpose.js | description": ["audits[identical-links-same-purpose].description"], "core/audits/accessibility/image-alt.js | title": ["audits[image-alt].title"], "core/audits/accessibility/image-alt.js | description": ["audits[image-alt].description"], "core/audits/accessibility/image-redundant-alt.js | title": ["audits[image-redundant-alt].title"], "core/audits/accessibility/image-redundant-alt.js | description": ["audits[image-redundant-alt].description"], "core/audits/accessibility/input-button-name.js | title": ["audits[input-button-name].title"], "core/audits/accessibility/input-button-name.js | description": ["audits[input-button-name].description"], "core/audits/accessibility/input-image-alt.js | title": ["audits[input-image-alt].title"], "core/audits/accessibility/input-image-alt.js | description": ["audits[input-image-alt].description"], "core/audits/accessibility/label-content-name-mismatch.js | title": ["audits[label-content-name-mismatch].title"], "core/audits/accessibility/label-content-name-mismatch.js | description": ["audits[label-content-name-mismatch].description"], "core/audits/accessibility/label.js | title": ["audits.label.title"], "core/audits/accessibility/label.js | description": ["audits.label.description"], "core/audits/accessibility/landmark-one-main.js | title": ["audits[landmark-one-main].title"], "core/audits/accessibility/landmark-one-main.js | description": ["audits[landmark-one-main].description"], "core/audits/accessibility/link-name.js | title": ["audits[link-name].title"], "core/audits/accessibility/link-name.js | description": ["audits[link-name].description"], "core/audits/accessibility/link-in-text-block.js | title": ["audits[link-in-text-block].title"], "core/audits/accessibility/link-in-text-block.js | description": ["audits[link-in-text-block].description"], "core/audits/accessibility/list.js | title": ["audits.list.title"], "core/audits/accessibility/list.js | description": ["audits.list.description"], "core/audits/accessibility/listitem.js | title": ["audits.listitem.title"], "core/audits/accessibility/listitem.js | description": ["audits.listitem.description"], "core/audits/accessibility/meta-refresh.js | title": ["audits[meta-refresh].title"], "core/audits/accessibility/meta-refresh.js | description": ["audits[meta-refresh].description"], "core/audits/accessibility/meta-viewport.js | title": ["audits[meta-viewport].title"], "core/audits/accessibility/meta-viewport.js | description": ["audits[meta-viewport].description"], "core/audits/accessibility/object-alt.js | title": ["audits[object-alt].title"], "core/audits/accessibility/object-alt.js | description": ["audits[object-alt].description"], "core/audits/accessibility/select-name.js | title": ["audits[select-name].title"], "core/audits/accessibility/select-name.js | description": ["audits[select-name].description"], "core/audits/accessibility/skip-link.js | title": ["audits[skip-link].title"], "core/audits/accessibility/skip-link.js | description": ["audits[skip-link].description"], "core/audits/accessibility/tabindex.js | title": ["audits.tabindex.title"], "core/audits/accessibility/tabindex.js | description": ["audits.tabindex.description"], "core/audits/accessibility/table-duplicate-name.js | title": ["audits[table-duplicate-name].title"], "core/audits/accessibility/table-duplicate-name.js | description": ["audits[table-duplicate-name].description"], "core/audits/accessibility/table-fake-caption.js | title": ["audits[table-fake-caption].title"], "core/audits/accessibility/table-fake-caption.js | description": ["audits[table-fake-caption].description"], "core/audits/accessibility/target-size.js | title": ["audits[target-size].title"], "core/audits/accessibility/target-size.js | description": ["audits[target-size].description"], "core/audits/accessibility/td-has-header.js | title": ["audits[td-has-header].title"], "core/audits/accessibility/td-has-header.js | description": ["audits[td-has-header].description"], "core/audits/accessibility/td-headers-attr.js | title": ["audits[td-headers-attr].title"], "core/audits/accessibility/td-headers-attr.js | description": ["audits[td-headers-attr].description"], "core/audits/accessibility/th-has-data-cells.js | title": ["audits[th-has-data-cells].title"], "core/audits/accessibility/th-has-data-cells.js | description": ["audits[th-has-data-cells].description"], "core/audits/accessibility/valid-lang.js | title": ["audits[valid-lang].title"], "core/audits/accessibility/valid-lang.js | description": ["audits[valid-lang].description"], "core/audits/accessibility/video-caption.js | title": ["audits[video-caption].title"], "core/audits/accessibility/video-caption.js | description": ["audits[video-caption].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": ["audits[uses-long-cache-ttl].title"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": ["audits[uses-long-cache-ttl].description"], "core/audits/byte-efficiency/total-byte-weight.js | title": ["audits[total-byte-weight].title"], "core/audits/byte-efficiency/total-byte-weight.js | description": ["audits[total-byte-weight].description"], "core/audits/byte-efficiency/offscreen-images.js | title": ["audits[offscreen-images].title"], "core/audits/byte-efficiency/offscreen-images.js | description": ["audits[offscreen-images].description"], "core/audits/byte-efficiency/render-blocking-resources.js | title": ["audits[render-blocking-resources].title"], "core/audits/byte-efficiency/render-blocking-resources.js | description": ["audits[render-blocking-resources].description"], "core/audits/byte-efficiency/unminified-css.js | title": ["audits[unminified-css].title"], "core/audits/byte-efficiency/unminified-css.js | description": ["audits[unminified-css].description"], "core/audits/byte-efficiency/unminified-javascript.js | title": ["audits[unminified-javascript].title"], "core/audits/byte-efficiency/unminified-javascript.js | description": ["audits[unminified-javascript].description"], "core/audits/byte-efficiency/unused-css-rules.js | title": ["audits[unused-css-rules].title"], "core/audits/byte-efficiency/unused-css-rules.js | description": ["audits[unused-css-rules].description"], "core/audits/byte-efficiency/unused-javascript.js | title": ["audits[unused-javascript].title"], "core/audits/byte-efficiency/unused-javascript.js | description": ["audits[unused-javascript].description"], "core/audits/byte-efficiency/modern-image-formats.js | title": ["audits[modern-image-formats].title"], "core/audits/byte-efficiency/modern-image-formats.js | description": ["audits[modern-image-formats].description"], "core/audits/byte-efficiency/uses-optimized-images.js | title": ["audits[uses-optimized-images].title"], "core/audits/byte-efficiency/uses-optimized-images.js | description": ["audits[uses-optimized-images].description"], "core/audits/byte-efficiency/uses-text-compression.js | title": ["audits[uses-text-compression].title"], "core/audits/byte-efficiency/uses-text-compression.js | description": ["audits[uses-text-compression].description"], "core/audits/byte-efficiency/uses-responsive-images.js | title": ["audits[uses-responsive-images].title"], "core/audits/byte-efficiency/uses-responsive-images.js | description": ["audits[uses-responsive-images].description"], "core/audits/byte-efficiency/efficient-animated-content.js | title": ["audits[efficient-animated-content].title"], "core/audits/byte-efficiency/efficient-animated-content.js | description": ["audits[efficient-animated-content].description"], "core/audits/byte-efficiency/duplicated-javascript.js | title": ["audits[duplicated-javascript].title"], "core/audits/byte-efficiency/duplicated-javascript.js | description": ["audits[duplicated-javascript].description"], "core/audits/byte-efficiency/legacy-javascript.js | title": ["audits[legacy-javascript].title"], "core/audits/byte-efficiency/legacy-javascript.js | description": ["audits[legacy-javascript].description"], "core/audits/dobetterweb/doctype.js | title": ["audits.doctype.title"], "core/audits/dobetterweb/doctype.js | description": ["audits.doctype.description"], "core/audits/dobetterweb/charset.js | title": ["audits.charset.title"], "core/audits/dobetterweb/charset.js | description": ["audits.charset.description"], "core/audits/dobetterweb/dom-size.js | title": ["audits[dom-size].title"], "core/audits/dobetterweb/dom-size.js | description": ["audits[dom-size].description"], "core/audits/dobetterweb/geolocation-on-start.js | title": ["audits[geolocation-on-start].title"], "core/audits/dobetterweb/geolocation-on-start.js | description": ["audits[geolocation-on-start].description"], "core/audits/dobetterweb/inspector-issues.js | title": ["audits[inspector-issues].title"], "core/audits/dobetterweb/inspector-issues.js | description": ["audits[inspector-issues].description"], "core/audits/dobetterweb/no-document-write.js | title": ["audits[no-document-write].title"], "core/audits/dobetterweb/no-document-write.js | description": ["audits[no-document-write].description"], "core/audits/dobetterweb/js-libraries.js | title": ["audits[js-libraries].title"], "core/audits/dobetterweb/js-libraries.js | description": ["audits[js-libraries].description"], "core/audits/dobetterweb/notification-on-start.js | title": ["audits[notification-on-start].title"], "core/audits/dobetterweb/notification-on-start.js | description": ["audits[notification-on-start].description"], "core/audits/dobetterweb/paste-preventing-inputs.js | title": ["audits[paste-preventing-inputs].title"], "core/audits/dobetterweb/paste-preventing-inputs.js | description": ["audits[paste-preventing-inputs].description"], "core/audits/dobetterweb/uses-http2.js | title": ["audits[uses-http2].title"], "core/audits/dobetterweb/uses-http2.js | description": ["audits[uses-http2].description"], "core/audits/dobetterweb/uses-passive-event-listeners.js | title": ["audits[uses-passive-event-listeners].title"], "core/audits/dobetterweb/uses-passive-event-listeners.js | description": ["audits[uses-passive-event-listeners].description"], "core/audits/seo/meta-description.js | title": ["audits[meta-description].title"], "core/audits/seo/meta-description.js | description": ["audits[meta-description].description"], "core/audits/seo/http-status-code.js | title": ["audits[http-status-code].title"], "core/audits/seo/http-status-code.js | description": ["audits[http-status-code].description"], "core/audits/seo/font-size.js | title": ["audits[font-size].title"], "core/audits/seo/font-size.js | description": ["audits[font-size].description"], "core/audits/seo/link-text.js | title": ["audits[link-text].title"], "core/audits/seo/link-text.js | description": ["audits[link-text].description"], "core/audits/seo/crawlable-anchors.js | title": ["audits[crawlable-anchors].title"], "core/audits/seo/crawlable-anchors.js | description": ["audits[crawlable-anchors].description"], "core/audits/seo/is-crawlable.js | title": ["audits[is-crawlable].title"], "core/audits/seo/is-crawlable.js | description": ["audits[is-crawlable].description"], "core/audits/seo/robots-txt.js | title": ["audits[robots-txt].title"], "core/audits/seo/robots-txt.js | description": ["audits[robots-txt].description"], "core/audits/seo/hreflang.js | title": ["audits.hreflang.title"], "core/audits/seo/hreflang.js | description": ["audits.hreflang.description"], "core/audits/seo/canonical.js | title": ["audits.canonical.title"], "core/audits/seo/canonical.js | description": ["audits.canonical.description"], "core/audits/seo/manual/structured-data.js | title": ["audits[structured-data].title"], "core/audits/seo/manual/structured-data.js | description": ["audits[structured-data].description"], "core/audits/bf-cache.js | title": ["audits[bf-cache].title"], "core/audits/bf-cache.js | description": ["audits[bf-cache].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | title": ["audits[cache-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | description": ["audits[cache-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | title": ["audits[cls-culprits-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | description": ["audits[cls-culprits-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | title": ["audits[document-latency-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | description": ["audits[document-latency-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | title": ["audits[dom-size-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | description": ["audits[dom-size-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | title": ["audits[duplicated-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | description": ["audits[duplicated-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | title": ["audits[font-display-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | description": ["audits[font-display-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | title": ["audits[forced-reflow-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | description": ["audits[forced-reflow-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | title": ["audits[image-delivery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | description": ["audits[image-delivery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | title": ["audits[interaction-to-next-paint-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | description": ["audits[interaction-to-next-paint-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | title": ["audits[lcp-discovery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | description": ["audits[lcp-discovery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | title": ["audits[lcp-phases-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | description": ["audits[lcp-phases-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | title": ["audits[legacy-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | description": ["audits[legacy-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | title": ["audits[modern-http-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | description": ["audits[modern-http-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | title": ["audits[network-dependency-tree-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | description": ["audits[network-dependency-tree-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | title": ["audits[render-blocking-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | description": ["audits[render-blocking-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | title": ["audits[third-parties-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | description": ["audits[third-parties-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | title": ["audits[viewport-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | description": ["audits[viewport-insight].description"], "core/config/default-config.js | performanceCategoryTitle": ["categories.performance.title"], "core/config/default-config.js | a11yCategoryTitle": ["categories.accessibility.title"], "core/config/default-config.js | a11yCategoryDescription": ["categories.accessibility.description"], "core/config/default-config.js | a11yCategoryManualDescription": ["categories.accessibility.manualDescription"], "core/config/default-config.js | bestPracticesCategoryTitle": ["categories[best-practices].title"], "core/config/default-config.js | seoCategoryTitle": ["categories.seo.title"], "core/config/default-config.js | seoCategoryDescription": ["categories.seo.description"], "core/config/default-config.js | seoCategoryManualDescription": ["categories.seo.manualDescription"], "core/config/default-config.js | metricGroupTitle": ["categoryGroups.metrics.title"], "core/config/default-config.js | insightsGroupTitle": ["categoryGroups.insights.title"], "core/config/default-config.js | insightsGroupDescription": ["categoryGroups.insights.description"], "core/config/default-config.js | diagnosticsGroupTitle": ["categoryGroups.diagnostics.title"], "core/config/default-config.js | diagnosticsGroupDescription": ["categoryGroups.diagnostics.description"], "core/config/default-config.js | a11yBestPracticesGroupTitle": ["categoryGroups[a11y-best-practices].title"], "core/config/default-config.js | a11yBestPracticesGroupDescription": ["categoryGroups[a11y-best-practices].description"], "core/config/default-config.js | a11yColorContrastGroupTitle": ["categoryGroups[a11y-color-contrast].title"], "core/config/default-config.js | a11yColorContrastGroupDescription": ["categoryGroups[a11y-color-contrast].description"], "core/config/default-config.js | a11yNamesLabelsGroupTitle": ["categoryGroups[a11y-names-labels].title"], "core/config/default-config.js | a11yNamesLabelsGroupDescription": ["categoryGroups[a11y-names-labels].description"], "core/config/default-config.js | a11yNavigationGroupTitle": ["categoryGroups[a11y-navigation].title"], "core/config/default-config.js | a11yNavigationGroupDescription": ["categoryGroups[a11y-navigation].description"], "core/config/default-config.js | a11yAriaGroupTitle": ["categoryGroups[a11y-aria].title"], "core/config/default-config.js | a11yAriaGroupDescription": ["categoryGroups[a11y-aria].description"], "core/config/default-config.js | a11yLanguageGroupTitle": ["categoryGroups[a11y-language].title"], "core/config/default-config.js | a11yLanguageGroupDescription": ["categoryGroups[a11y-language].description"], "core/config/default-config.js | a11yAudioVideoGroupTitle": ["categoryGroups[a11y-audio-video].title"], "core/config/default-config.js | a11yAudioVideoGroupDescription": ["categoryGroups[a11y-audio-video].description"], "core/config/default-config.js | a11yTablesListsVideoGroupTitle": ["categoryGroups[a11y-tables-lists].title"], "core/config/default-config.js | a11yTablesListsVideoGroupDescription": ["categoryGroups[a11y-tables-lists].description"], "core/config/default-config.js | seoMobileGroupTitle": ["categoryGroups[seo-mobile].title"], "core/config/default-config.js | seoMobileGroupDescription": ["categoryGroups[seo-mobile].description"], "core/config/default-config.js | seoContentGroupTitle": ["categoryGroups[seo-content].title"], "core/config/default-config.js | seoContentGroupDescription": ["categoryGroups[seo-content].description"], "core/config/default-config.js | seoCrawlingGroupTitle": ["categoryGroups[seo-crawl].title"], "core/config/default-config.js | seoCrawlingGroupDescription": ["categoryGroups[seo-crawl].description"], "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": ["categoryGroups[best-practices-trust-safety].title"], "core/config/default-config.js | bestPracticesUXGroupTitle": ["categoryGroups[best-practices-ux].title"], "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": ["categoryGroups[best-practices-browser-compat].title"], "core/config/default-config.js | bestPracticesGeneralGroupTitle": ["categoryGroups[best-practices-general].title"]}}}